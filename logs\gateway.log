2025-07-21 17:05:31,397 - __main__ - INFO - 网关服务初始化开始
2025-07-21 17:05:31,454 - root - INFO - 生成SSH服务器密钥: ./keys/ssh_host_key
2025-07-21 17:05:31,456 - root - INFO - 创建SSH授权密钥文件: ./keys/authorized_keys
2025-07-21 17:05:31,458 - __main__ - INFO - 网关服务初始化完成
2025-07-21 17:05:31,458 - __main__ - INFO - 启动网关服务
2025-07-21 17:05:31,458 - gateway.session_manager - INFO - 启动会话管理器
2025-07-21 17:05:31,527 - asyncssh - INFO - Creating SSH listener on 0.0.0.0, port 2222
2025-07-21 17:05:31,530 - gateway.ssh_server - INFO - SSH服务启动成功，监听 0.0.0.0:2222
2025-07-21 17:05:31,531 - websockets.server - INFO - server listening on 0.0.0.0:8765
2025-07-21 17:05:31,532 - gateway.websocket_server - INFO - WebSocket服务启动成功，监听 ws://0.0.0.0:8765
2025-07-21 17:05:31,532 - __main__ - INFO - 网关服务启动成功
2025-07-21 17:07:27,424 - websockets.server - INFO - connection open
2025-07-21 17:07:27,425 - gateway.websocket_server - INFO - WebSocket客户端连接: 127.0.0.1:63044, 设备ID: device_A100
2025-07-21 17:07:27,426 - gateway.session_manager - INFO - 设备 device_A100 注册成功
2025-07-21 17:08:23,531 - asyncssh - INFO - [conn=0] Accepted SSH client connection
2025-07-21 17:08:23,532 - asyncssh - INFO - [conn=0]   Local address: 127.0.0.1, port 2222
2025-07-21 17:08:23,532 - asyncssh - INFO - [conn=0]   Peer address: 127.0.0.1, port 63140
2025-07-21 17:08:23,533 - gateway.ssh_server - INFO - SSH连接建立: ('127.0.0.1', 63140)
2025-07-21 17:08:27,310 - asyncssh - INFO - [conn=0] Beginning auth for user operator
2025-07-21 17:08:27,359 - gateway.ssh_server - INFO - 用户 operator 开始SSH认证
2025-07-21 17:08:27,363 - gateway.ssh_server - ERROR - 公钥认证时出错: 'gbk' codec can't decode byte 0xac in position 19: illegal multibyte sequence
2025-07-21 17:08:27,363 - gateway.ssh_server - WARNING - 用户 operator 公钥认证失败
2025-07-21 17:08:27,369 - gateway.ssh_server - ERROR - SSH连接异常断开: [WinError 64] 指定的网络名不再可用。
2025-07-21 17:12:19,595 - asyncssh - INFO - [conn=1] Accepted SSH client connection
2025-07-21 17:12:19,596 - asyncssh - INFO - [conn=1]   Local address: 127.0.0.1, port 2222
2025-07-21 17:12:19,596 - asyncssh - INFO - [conn=1]   Peer address: 127.0.0.1, port 63683
2025-07-21 17:12:19,598 - gateway.ssh_server - INFO - SSH连接建立: ('127.0.0.1', 63683)
2025-07-21 17:12:19,610 - asyncssh - INFO - [conn=1] Beginning auth for user operator
2025-07-21 17:12:19,664 - gateway.ssh_server - INFO - 用户 operator 开始SSH认证
2025-07-21 17:12:19,667 - gateway.ssh_server - INFO - 用户 operator 公钥认证成功
2025-07-21 17:12:19,694 - gateway.ssh_server - INFO - 用户 operator 公钥认证成功
2025-07-21 17:12:19,695 - asyncssh - INFO - [conn=1] Auth for user operator succeeded
2025-07-21 17:12:19,697 - asyncssh - INFO - [conn=1] Sending server host keys disabled
2025-07-21 17:12:19,698 - asyncssh - INFO - [conn=1, chan=0] New SSH session requested
2025-07-21 17:12:19,700 - asyncssh - INFO - [conn=1, chan=0]   Command: device_A100
2025-07-21 17:12:19,701 - asyncssh - INFO - [conn=1, chan=0] Closing channel due to connection close
2025-07-21 17:12:19,702 - asyncssh - INFO - [conn=1, chan=0] Channel closed: 'NoneType' object has no attribute 'session_manager'
2025-07-21 17:12:19,702 - gateway.ssh_server - ERROR - SSH连接异常断开: 'NoneType' object has no attribute 'session_manager'
2025-07-21 17:12:31,492 - gateway.session_manager - INFO - 清理过期设备: device_A100
2025-07-21 17:12:31,494 - gateway.session_manager - INFO - 设备 device_A100 注销成功
2025-07-21 17:13:01,021 - asyncssh - INFO - [conn=2] Accepted SSH client connection
2025-07-21 17:13:01,022 - asyncssh - INFO - [conn=2]   Local address: 127.0.0.1, port 2222
2025-07-21 17:13:01,022 - asyncssh - INFO - [conn=2]   Peer address: 127.0.0.1, port 63755
2025-07-21 17:13:01,024 - gateway.ssh_server - INFO - SSH连接建立: ('127.0.0.1', 63755)
2025-07-21 17:13:01,037 - asyncssh - INFO - [conn=2] Beginning auth for user operator
2025-07-21 17:13:01,092 - gateway.ssh_server - INFO - 用户 operator 开始SSH认证
2025-07-21 17:13:01,094 - gateway.ssh_server - INFO - 用户 operator 公钥认证成功
2025-07-21 17:13:01,121 - gateway.ssh_server - INFO - 用户 operator 公钥认证成功
2025-07-21 17:13:01,122 - asyncssh - INFO - [conn=2] Auth for user operator succeeded
2025-07-21 17:13:01,123 - asyncssh - INFO - [conn=2] Sending server host keys disabled
2025-07-21 17:13:01,126 - asyncssh - INFO - [conn=2, chan=0] New SSH session requested
2025-07-21 17:13:01,128 - asyncssh - INFO - [conn=2, chan=0]   Command: device_A100
2025-07-21 17:13:01,129 - asyncssh - INFO - [conn=2, chan=0] Closing channel due to connection close
2025-07-21 17:13:01,129 - asyncssh - INFO - [conn=2, chan=0] Channel closed: 'NoneType' object has no attribute 'session_manager'
2025-07-21 17:13:01,130 - gateway.ssh_server - ERROR - SSH连接异常断开: 'NoneType' object has no attribute 'session_manager'
