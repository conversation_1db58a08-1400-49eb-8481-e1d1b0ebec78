# API 文档

## 概述

堡垒机到微信小程序通信网关提供两个主要的API接口：
1. SSH接口 - 供堡垒机连接使用
2. WebSocket接口 - 供微信小程序连接使用

## SSH接口

### 连接方式

```bash
ssh <username>@<gateway_host> -p <ssh_port> <device_id>
```

### 参数说明

- `username`: SSH用户名（默认为 `operator`）
- `gateway_host`: 网关服务器地址
- `ssh_port`: SSH服务端口（默认为 2222）
- `device_id`: 目标设备ID，格式为 `device_[A-Za-z0-9_]+`

### 认证方式

支持两种认证方式：

1. **公钥认证（推荐）**
   - 将公钥添加到 `keys/authorized_keys` 文件中
   - 格式：`ssh-rsa AAAAB3NzaC1yc2E... user@host`

2. **密码认证**
   - 在配置文件中启用 `password_auth: true`
   - 默认用户名/密码：`operator/password`

### 使用示例

```bash
# 连接到设备 device_A100
ssh <EMAIL> -p 2222 device_A100

# 使用公钥认证
ssh -i ~/.ssh/id_rsa <EMAIL> -p 2222 device_B200
```

### 错误处理

- 如果设备ID格式无效，返回错误信息并关闭连接
- 如果目标设备不在线，返回 "Error: Device 'device_xxx' is not online."
- 认证失败时，按照标准SSH协议处理

## WebSocket接口

### 连接方式

```
ws://gateway_host:ws_port/device_id
wss://gateway_host:ws_port/device_id  # SSL启用时
```

### URL格式

支持两种URL格式：
1. `ws://host:port/device_id`
2. `ws://host:port/device/device_id`

### 消息格式

所有消息使用JSON格式，包含 `type` 和 `payload` 字段。

#### 客户端发送的消息类型

##### 1. 数据消息
```json
{
  "type": "data",
  "payload": "ls -l\n"
}
```

##### 2. 心跳消息
```json
{
  "type": "heartbeat",
  "payload": {
    "timestamp": 1234567890
  }
}
```

##### 3. 状态消息
```json
{
  "type": "status",
  "payload": {
    "status": "ready",
    "info": "设备就绪"
  }
}
```

#### 服务端发送的消息类型

##### 1. 连接确认
```json
{
  "type": "connected",
  "payload": {
    "device_id": "device_A100",
    "status": "online"
  }
}
```

##### 2. 终端数据
```json
{
  "type": "data",
  "payload": "total 0\ndrwxr-xr-x 1 <USER> <GROUP> 4096 ..."
}
```

##### 3. 窗口大小调整
```json
{
  "type": "resize",
  "payload": {
    "rows": 24,
    "cols": 80
  }
}
```

##### 4. 心跳确认
```json
{
  "type": "heartbeat_ack",
  "payload": {
    "timestamp": 1234567890
  }
}
```

##### 5. 错误消息
```json
{
  "type": "error",
  "payload": {
    "message": "错误描述"
  }
}
```

### 连接流程

1. 客户端连接到WebSocket服务器
2. 服务器从URL中提取device_id
3. 验证device_id格式
4. 注册设备到会话管理器
5. 发送连接确认消息
6. 开始消息循环处理

### 使用示例

#### JavaScript客户端示例

```javascript
// 连接到WebSocket服务器
const ws = new WebSocket('ws://localhost:8765/device_A100');

ws.onopen = function(event) {
    console.log('WebSocket连接已建立');
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    console.log('收到消息:', message);
    
    switch(message.type) {
        case 'connected':
            console.log('设备已连接:', message.payload.device_id);
            break;
        case 'data':
            // 处理终端数据
            console.log('终端数据:', message.payload);
            break;
        case 'resize':
            // 处理窗口大小调整
            console.log('窗口大小:', message.payload);
            break;
    }
};

// 发送数据
function sendCommand(command) {
    const message = {
        type: 'data',
        payload: command
    };
    ws.send(JSON.stringify(message));
}

// 发送心跳
function sendHeartbeat() {
    const message = {
        type: 'heartbeat',
        payload: {
            timestamp: Date.now()
        }
    };
    ws.send(JSON.stringify(message));
}
```

#### Python客户端示例

```python
import asyncio
import websockets
import json

async def websocket_client():
    uri = "ws://localhost:8765/device_A100"
    
    async with websockets.connect(uri) as websocket:
        # 发送心跳
        heartbeat_msg = {
            "type": "heartbeat",
            "payload": {"timestamp": 1234567890}
        }
        await websocket.send(json.dumps(heartbeat_msg))
        
        # 接收消息
        async for message in websocket:
            data = json.loads(message)
            print(f"收到消息: {data}")
            
            if data["type"] == "data":
                # 处理终端数据
                print(f"终端数据: {data['payload']}")

# 运行客户端
asyncio.run(websocket_client())
```

## 数据传输协议

### 编码处理

根据技术方案，网关使用JSON格式传输数据。对于可能包含无效字节序列的终端数据，采用以下处理策略：

1. **编码**: 使用 `data.decode('utf-8', errors='replace')` 将字节数据转换为字符串
2. **解码**: 使用 `text.encode('utf-8', errors='replace')` 将字符串转换为字节数据
3. **错误处理**: 无法解码的字节会被替换为占位符（如 `?`）

### 数据完整性

- 对于纯文本命令（ls, cat, echo）通常可以正常工作
- 对于二进制文件内容或复杂的图形化终端应用，可能会出现数据损坏
- 如遇到数据乱码问题，建议切换到Base64编码方案

## 错误码

### SSH错误

- 认证失败：标准SSH错误码
- 设备ID无效：连接关闭，返回错误信息
- 设备不在线：连接关闭，返回 "Error: Device 'xxx' is not online."

### WebSocket错误

- 连接路径无效：关闭连接，code=1000
- 设备ID格式无效：关闭连接，code=1000
- 设备注册失败：关闭连接，code=1000
- JSON解析错误：发送错误消息

## 性能指标

### 连接限制

- 最大并发连接数：100（可配置）
- 设备超时时间：300秒（可配置）
- SSH会话超时：3600秒（可配置）

### 心跳机制

- WebSocket心跳间隔：30秒
- WebSocket心跳超时：10秒
- 连接关闭超时：10秒

## 安全考虑

### SSH安全

- 推荐使用公钥认证
- 定期更换SSH密钥
- 限制SSH用户权限

### WebSocket安全

- 生产环境建议启用SSL/TLS
- 验证设备ID格式
- 实施连接频率限制

### 网络安全

- 配置防火墙规则
- 使用VPN或专用网络
- 定期更新SSL证书
