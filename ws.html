<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 演示</title>
    <style>
        body {
            font-family: sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }
        #container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        #connectionStatus {
            text-align: center;
            margin-bottom: 15px;
            font-weight: bold;
        }
        #messages {
            border: 1px solid #ccc;
            height: 300px;
            overflow-y: scroll;
            padding: 10px;
            margin-bottom: 15px;
            background-color: #e9e9e9;
            border-radius: 4px;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            background-color: #dff0d8;
            border-left: 4px solid #5cb85c;
            border-radius: 3px;
        }
        .received {
            background-color: #d9edf7;
            border-left-color: #337ab7;
        }
        .sent {
            background-color: #fcf8e3;
            border-left-color: #f0ad4e;
        }
        #inputArea {
            display: flex;
            margin-bottom: 15px;
        }
        #messageInput {
            flex-grow: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 10px;
        }
        button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background-color: #0056b3;
        }
        .controls {
            text-align: center;
        }
        .controls button {
            margin: 0 5px;
        }
        #deviceIdInput {
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div id="container">
        <h1>WebSocket 演示</h1>

        <div id="connectionControls">
            <label for="deviceIdInput">设备 ID:</label>
            <input type="text" id="deviceIdInput" placeholder="例如: device_A100">
            <button id="connectButton">连接</button>
            <button id="disconnectButton" disabled>断开连接</button>
        </div>

        <div id="connectionStatus">状态: 未连接</div>

        <div id="messages">
            <!-- 消息将在这里展示 -->
        </div>

        <div id="inputArea">
            <input type="text" id="messageInput" placeholder="输入消息...">
            <button id="sendButton" disabled>发送</button>
        </div>
    </div>

    <script>
        const connectButton = document.getElementById('connectButton');
        const disconnectButton = document.getElementById('disconnectButton');
        const sendButton = document.getElementById('sendButton');
        const messageInput = document.getElementById('messageInput');
        const messagesDiv = document.getElementById('messages');
        const connectionStatusDiv = document.getElementById('connectionStatus');
        const deviceIdInput = document.getElementById('deviceIdInput');

        let websocket = null;
        const serverUrl = 'ws://localhost:8765'; // 你的 WebSocket 服务器地址

        // -------------------- WebSocket 连接和事件处理 --------------------

        function connectWebSocket() {
            const deviceId = deviceIdInput.value.trim();
            if (!deviceId) {
                alert('请输入设备 ID！');
                return;
            }

            const url = `${serverUrl}/${deviceId}`;
            console.log(`尝试连接: ${url}`);
            connectionStatusDiv.textContent = `状态: 正在连接...`;

            websocket = new WebSocket(url);

            // 1. 连接成功
            websocket.onopen = function(event) {
                console.log('WebSocket 已连接');
                connectionStatusDiv.textContent = `状态: 已连接 (设备 ID: ${deviceId})`;
                connectButton.disabled = true;
                disconnectButton.disabled = false;
                sendButton.disabled = false;
                messageInput.disabled = false;
                addMessage(`已连接到服务器: ${url}`, 'system');
            };

            // 2. 收到消息
            websocket.onmessage = function(event) {
                console.log('收到消息:', event.data);
                addMessage(`收到: ${event.data}`, 'received');
            };

            // 3. 连接关闭
            websocket.onclose = function(event) {
                console.log('WebSocket 已关闭', event);
                connectionStatusDiv.textContent = `状态: 已断开 (${event.code}: ${event.reason || '无原因'})`;
                connectButton.disabled = false;
                disconnectButton.disabled = true;
                sendButton.disabled = true;
                messageInput.disabled = true;
                addMessage(`连接已关闭: ${event.code}`, 'system');
                websocket = null; // 清空 websocket 对象
            };

            // 4. 发生错误
            websocket.onerror = function(event) {
                console.error('WebSocket 发生错误:', event);
                connectionStatusDiv.textContent = '状态: 发生错误';
                addMessage('WebSocket 发生错误，请检查服务器或网络连接。', 'error');
                // 错误发生时，连接也可能被关闭，所以这里不需要再调用 disconnect
            };
        }

        function disconnectWebSocket() {
            if (websocket) {
                websocket.close();
            }
        }

        // -------------------- 消息发送和展示 --------------------

        function sendMessage() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                const message = messageInput.value.trim();
                if (message) {
                    websocket.send(message);
                    console.log('发送消息:', message);
                    addMessage(`发送: ${message}`, 'sent');
                    messageInput.value = ''; // 清空输入框
                }
            } else {
                alert('WebSocket 未连接，无法发送消息。');
            }
        }

        // 辅助函数：将消息添加到消息区域
        function addMessage(msg, type = 'normal') {
            const messageElement = document.createElement('div');
            messageElement.classList.add('message', type);
            messageElement.textContent = msg;
            messagesDiv.appendChild(messageElement);
            // 滚动到底部
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // -------------------- 事件监听器 --------------------

        connectButton.addEventListener('click', connectWebSocket);
        disconnectButton.addEventListener('click', disconnectWebSocket);

        sendButton.addEventListener('click', sendMessage);

        // 允许按回车键发送消息
        messageInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        });

        // 允许按回车键连接
        deviceIdInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                connectWebSocket();
            }
        });

        // 页面关闭时尝试断开连接 (可选)
        window.addEventListener('beforeunload', function() {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                // 注意：在 beforeunload 中发送消息可能不可靠，通常用于清理
                // websocket.send("Client is disconnecting");
                websocket.close();
            }
        });

    </script>
</body>
</html>
