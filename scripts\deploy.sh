#!/bin/bash

# 堡垒机到微信小程序通信网关部署脚本

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
SERVICE_NAME="oms-gateway"
SERVICE_USER="oms-gateway"
INSTALL_DIR="/opt/oms-gateway"
SERVICE_FILE="/etc/systemd/system/${SERVICE_NAME}.service"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root权限运行此脚本"
        exit 1
    fi
}

# 创建服务用户
create_service_user() {
    log_info "创建服务用户..."
    
    if id "$SERVICE_USER" &>/dev/null; then
        log_info "用户 $SERVICE_USER 已存在"
    else
        useradd -r -s /bin/false -d "$INSTALL_DIR" "$SERVICE_USER"
        log_info "用户 $SERVICE_USER 创建成功"
    fi
}

# 安装系统依赖
install_system_dependencies() {
    log_info "安装系统依赖..."
    
    # 检测操作系统
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        apt-get update
        apt-get install -y python3 python3-pip python3-venv git
    elif [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        yum update -y
        yum install -y python3 python3-pip git
    else
        log_warn "未知的操作系统，请手动安装Python3和pip"
    fi
}

# 复制项目文件
copy_project_files() {
    log_info "复制项目文件..."
    
    # 创建安装目录
    mkdir -p "$INSTALL_DIR"
    
    # 复制文件
    cp -r "$PROJECT_DIR"/* "$INSTALL_DIR/"
    
    # 设置权限
    chown -R "$SERVICE_USER:$SERVICE_USER" "$INSTALL_DIR"
    chmod +x "$INSTALL_DIR/scripts/start.sh"
    
    log_info "项目文件复制完成"
}

# 安装Python依赖
install_python_dependencies() {
    log_info "安装Python依赖..."
    
    cd "$INSTALL_DIR"
    
    # 创建虚拟环境
    sudo -u "$SERVICE_USER" python3 -m venv venv
    
    # 安装依赖
    sudo -u "$SERVICE_USER" bash -c "source venv/bin/activate && pip install -r requirements.txt"
    
    log_info "Python依赖安装完成"
}

# 创建systemd服务文件
create_systemd_service() {
    log_info "创建systemd服务文件..."
    
    cat > "$SERVICE_FILE" << EOF
[Unit]
Description=OMS Gateway Service
After=network.target

[Service]
Type=simple
User=$SERVICE_USER
Group=$SERVICE_USER
WorkingDirectory=$INSTALL_DIR
Environment=PATH=$INSTALL_DIR/venv/bin
ExecStart=$INSTALL_DIR/venv/bin/python $INSTALL_DIR/main.py
Restart=always
RestartSec=10

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$INSTALL_DIR

[Install]
WantedBy=multi-user.target
EOF

    # 重新加载systemd
    systemctl daemon-reload
    
    log_info "systemd服务文件创建完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    # 默认端口
    SSH_PORT=2222
    WS_PORT=8765
    
    # 从配置文件读取端口（如果存在）
    if [ -f "$INSTALL_DIR/config.yaml" ]; then
        SSH_PORT=$(python3 -c "
import yaml
try:
    with open('$INSTALL_DIR/config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    print(config.get('ssh', {}).get('port', 2222))
except:
    print(2222)
" 2>/dev/null || echo 2222)
        
        WS_PORT=$(python3 -c "
import yaml
try:
    with open('$INSTALL_DIR/config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    print(config.get('websocket', {}).get('port', 8765))
except:
    print(8765)
" 2>/dev/null || echo 8765)
    fi
    
    # 配置iptables或ufw
    if command -v ufw &> /dev/null; then
        ufw allow "$SSH_PORT/tcp"
        ufw allow "$WS_PORT/tcp"
        log_info "ufw防火墙规则已添加"
    elif command -v firewall-cmd &> /dev/null; then
        firewall-cmd --permanent --add-port="$SSH_PORT/tcp"
        firewall-cmd --permanent --add-port="$WS_PORT/tcp"
        firewall-cmd --reload
        log_info "firewalld防火墙规则已添加"
    else
        log_warn "未检测到防火墙管理工具，请手动开放端口 $SSH_PORT 和 $WS_PORT"
    fi
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    systemctl enable "$SERVICE_NAME"
    systemctl start "$SERVICE_NAME"
    
    # 检查服务状态
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "服务启动成功"
    else
        log_error "服务启动失败"
        systemctl status "$SERVICE_NAME"
        exit 1
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "========================================"
    echo "部署完成！"
    echo "========================================"
    echo "服务名称: $SERVICE_NAME"
    echo "安装目录: $INSTALL_DIR"
    echo "配置文件: $INSTALL_DIR/config.yaml"
    echo "日志文件: $INSTALL_DIR/logs/gateway.log"
    echo ""
    echo "服务管理命令:"
    echo "  启动服务: systemctl start $SERVICE_NAME"
    echo "  停止服务: systemctl stop $SERVICE_NAME"
    echo "  重启服务: systemctl restart $SERVICE_NAME"
    echo "  查看状态: systemctl status $SERVICE_NAME"
    echo "  查看日志: journalctl -u $SERVICE_NAME -f"
    echo ""
    echo "请根据需要修改配置文件，然后重启服务"
    echo "========================================"
}

# 卸载服务
uninstall_service() {
    log_info "卸载服务..."
    
    # 停止并禁用服务
    systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    systemctl disable "$SERVICE_NAME" 2>/dev/null || true
    
    # 删除服务文件
    rm -f "$SERVICE_FILE"
    systemctl daemon-reload
    
    # 删除安装目录
    rm -rf "$INSTALL_DIR"
    
    # 删除用户
    userdel "$SERVICE_USER" 2>/dev/null || true
    
    log_info "服务卸载完成"
}

# 主函数
main() {
    case "${1:-install}" in
        install)
            echo "========================================"
            echo "堡垒机到微信小程序通信网关部署脚本"
            echo "========================================"
            
            check_permissions
            create_service_user
            install_system_dependencies
            copy_project_files
            install_python_dependencies
            create_systemd_service
            configure_firewall
            start_service
            show_deployment_info
            ;;
        uninstall)
            echo "========================================"
            echo "卸载堡垒机到微信小程序通信网关"
            echo "========================================"
            
            check_permissions
            uninstall_service
            ;;
        *)
            echo "用法: $0 [install|uninstall]"
            echo "  install   - 安装服务（默认）"
            echo "  uninstall - 卸载服务"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
