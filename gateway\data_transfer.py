"""
数据转发和协议转换模块

实现SSH和WebSocket之间的双向数据转发，处理终端数据和窗口调整。
根据方案文档，使用JSON格式传输数据，对于可能包含无效字节序列的终端数据，
使用错误处理策略进行编码/解码。
"""

import asyncio
import json
import logging
from typing import Optional, Any, Dict
from websockets.server import WebSocketServerProtocol
from .utils import safe_decode, safe_encode

logger = logging.getLogger(__name__)


class DataTransfer:
    """数据转发器类"""
    
    def __init__(self, ssh_process, websocket: WebSocketServerProtocol, device_id: str):
        """
        初始化数据转发器
        
        Args:
            ssh_process: SSH进程对象
            websocket: WebSocket连接对象
            device_id: 设备ID
        """
        self.ssh_process = ssh_process
        self.websocket = websocket
        self.device_id = device_id
        self._running = False
        self._ssh_to_ws_task = None
        self._ws_to_ssh_task = None
        
    async def start(self):
        """启动数据转发"""
        if self._running:
            return
            
        self._running = True
        logger.info(f"启动数据转发，设备: {self.device_id}")
        
        try:
            # 创建双向数据转发任务
            self._ssh_to_ws_task = asyncio.create_task(self._ssh_to_websocket())
            self._ws_to_ssh_task = asyncio.create_task(self._websocket_to_ssh())
            
            # 等待任一任务完成（通常是连接断开）
            done, pending = await asyncio.wait(
                [self._ssh_to_ws_task, self._ws_to_ssh_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # 取消剩余任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                    
        except Exception as e:
            logger.error(f"数据转发出错: {e}")
        finally:
            self._running = False
            logger.info(f"数据转发结束，设备: {self.device_id}")
            
    async def stop(self):
        """停止数据转发"""
        if not self._running:
            return
            
        self._running = False
        
        # 取消转发任务
        if self._ssh_to_ws_task:
            self._ssh_to_ws_task.cancel()
        if self._ws_to_ssh_task:
            self._ws_to_ssh_task.cancel()
            
        logger.info(f"停止数据转发，设备: {self.device_id}")
        
    async def _ssh_to_websocket(self):
        """SSH到WebSocket的数据转发"""
        try:
            while self._running:
                # 从SSH进程读取数据
                data = await self._read_ssh_data()
                if not data:
                    break
                    
                # 转换为JSON格式并发送到WebSocket
                await self._send_to_websocket("data", data)
                
        except asyncio.CancelledError:
            logger.debug("SSH到WebSocket转发任务被取消")
        except Exception as e:
            logger.error(f"SSH到WebSocket转发出错: {e}")
            
    async def _websocket_to_ssh(self):
        """WebSocket到SSH的数据转发"""
        try:
            while self._running:
                # 从WebSocket接收消息
                message = await self._receive_from_websocket()
                if not message:
                    break
                    
                # 处理不同类型的消息
                msg_type = message.get('type')
                payload = message.get('payload')
                
                if msg_type == 'data':
                    # 终端数据，转发到SSH
                    await self._send_to_ssh(payload)
                elif msg_type == 'resize':
                    # 窗口大小调整
                    await self._handle_resize(payload)
                else:
                    logger.debug(f"忽略未知消息类型: {msg_type}")
                    
        except asyncio.CancelledError:
            logger.debug("WebSocket到SSH转发任务被取消")
        except Exception as e:
            logger.error(f"WebSocket到SSH转发出错: {e}")
            
    async def _read_ssh_data(self) -> Optional[bytes]:
        """从SSH进程读取数据"""
        try:
            # 这里需要根据实际的SSH进程实现来读取数据
            # 由于asyncssh的特殊性，我们需要在SSH进程中实现数据读取
            # 这个方法将在SSH进程中被调用
            return None
        except Exception as e:
            logger.error(f"读取SSH数据失败: {e}")
            return None
            
    async def _send_to_websocket(self, msg_type: str, data: Any):
        """发送数据到WebSocket"""
        try:
            # 根据方案文档，使用JSON格式，对字节数据进行安全解码
            if isinstance(data, bytes):
                payload = safe_decode(data)
            else:
                payload = data
                
            message = {
                "type": msg_type,
                "payload": payload
            }
            
            json_message = json.dumps(message, ensure_ascii=False)
            await self.websocket.send(json_message)
            
            logger.debug(f"发送到WebSocket: {msg_type}, 长度: {len(str(payload))}")
            
        except Exception as e:
            logger.error(f"发送到WebSocket失败: {e}")
            
    async def _receive_from_websocket(self) -> Optional[Dict[str, Any]]:
        """从WebSocket接收消息"""
        try:
            message = await self.websocket.recv()
            data = json.loads(message)
            
            logger.debug(f"从WebSocket接收: {data.get('type')}")
            return data
            
        except json.JSONDecodeError as e:
            logger.error(f"WebSocket消息JSON解析错误: {e}")
            return None
        except Exception as e:
            logger.error(f"从WebSocket接收消息失败: {e}")
            return None
            
    async def _send_to_ssh(self, data: str):
        """发送数据到SSH进程"""
        try:
            # 将字符串安全编码为字节数据
            byte_data = safe_encode(data)
            
            # 发送到SSH进程
            self.ssh_process.write_data(byte_data)
            
            logger.debug(f"发送到SSH: 长度 {len(byte_data)}")
            
        except Exception as e:
            logger.error(f"发送到SSH失败: {e}")
            
    async def _handle_resize(self, resize_data: Dict[str, int]):
        """处理终端窗口大小调整"""
        try:
            rows = resize_data.get('rows', 24)
            cols = resize_data.get('cols', 80)
            
            # 通知SSH进程终端大小变化
            if hasattr(self.ssh_process, 'terminal_size_changed'):
                self.ssh_process.terminal_size_changed(cols, rows, 0, 0)
                
            logger.debug(f"终端大小调整: {rows}x{cols}")
            
        except Exception as e:
            logger.error(f"处理终端大小调整失败: {e}")
            
    async def handle_terminal_resize(self, rows: int, cols: int):
        """处理来自SSH的终端大小调整"""
        try:
            await self._send_to_websocket("resize", {
                "rows": rows,
                "cols": cols
            })
            
            logger.debug(f"转发终端大小调整: {rows}x{cols}")
            
        except Exception as e:
            logger.error(f"转发终端大小调整失败: {e}")
            
    async def send_ssh_data_to_websocket(self, data: bytes):
        """将SSH数据发送到WebSocket（由SSH进程调用）"""
        if self._running:
            await self._send_to_websocket("data", data)


class TerminalDataProcessor:
    """终端数据处理器"""
    
    @staticmethod
    def process_terminal_output(data: bytes) -> str:
        """
        处理终端输出数据
        
        根据方案文档的建议，使用错误处理策略来处理可能包含
        无效字节序列的终端数据。
        
        Args:
            data: 原始终端数据
            
        Returns:
            str: 处理后的字符串数据
        """
        return safe_decode(data, errors='replace')
        
    @staticmethod
    def process_terminal_input(text: str) -> bytes:
        """
        处理终端输入数据
        
        Args:
            text: 输入文本
            
        Returns:
            bytes: 编码后的字节数据
        """
        return safe_encode(text, errors='replace')
        
    @staticmethod
    def is_control_sequence(data: bytes) -> bool:
        """
        检查数据是否包含控制序列
        
        Args:
            data: 数据
            
        Returns:
            bool: 是否包含控制序列
        """
        # 检查ANSI转义序列
        return b'\x1b[' in data or b'\x1b(' in data or b'\x1b)' in data
        
    @staticmethod
    def extract_printable_text(data: bytes) -> str:
        """
        提取可打印文本（去除控制序列）
        
        Args:
            data: 原始数据
            
        Returns:
            str: 可打印文本
        """
        # 简单的控制序列过滤（实际应用中可能需要更复杂的处理）
        text = safe_decode(data)
        
        # 移除常见的ANSI转义序列
        import re
        ansi_escape = re.compile(r'\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])')
        return ansi_escape.sub('', text)
