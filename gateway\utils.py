"""
工具函数模块

提供通用的工具函数，包括配置加载、日志设置、SSL证书生成等。
"""

import os
import yaml
import logging
import logging.handlers
from typing import Dict, Any
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives import hashes
import datetime
import ipaddress


def load_config(config_path: str = "config.yaml") -> Dict[str, Any]:
    """
    加载配置文件
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict[str, Any]: 配置字典
    """
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except FileNotFoundError:
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    except yaml.YAMLError as e:
        raise ValueError(f"配置文件格式错误: {e}")


def setup_logging(config: Dict[str, Any]):
    """
    设置日志系统
    
    Args:
        config: 日志配置
    """
    log_config = config.get('log', {})
    level = getattr(logging, log_config.get('level', 'INFO').upper())
    log_file = log_config.get('file', './logs/gateway.log')
    console = log_config.get('console', True)
    max_size = log_config.get('max_size', '10MB')
    backup_count = log_config.get('backup_count', 5)
    
    # 创建日志目录
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 解析文件大小
    if isinstance(max_size, str):
        if max_size.endswith('MB'):
            max_bytes = int(max_size[:-2]) * 1024 * 1024
        elif max_size.endswith('KB'):
            max_bytes = int(max_size[:-2]) * 1024
        else:
            max_bytes = int(max_size)
    else:
        max_bytes = max_size
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 文件处理器（带轮转）
    if log_file:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=max_bytes, backupCount=backup_count, encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 控制台处理器
    if console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)


def ensure_directory(path: str):
    """
    确保目录存在
    
    Args:
        path: 目录路径
    """
    if not os.path.exists(path):
        os.makedirs(path)


def generate_ssh_key(key_path: str):
    """
    生成SSH服务器密钥
    
    Args:
        key_path: 密钥文件路径
    """
    if os.path.exists(key_path):
        return
        
    # 确保目录存在
    key_dir = os.path.dirname(key_path)
    if key_dir:
        ensure_directory(key_dir)
    
    # 生成RSA密钥对
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )
    
    # 保存私钥
    with open(key_path, 'wb') as f:
        f.write(private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.OpenSSH,
            encryption_algorithm=serialization.NoEncryption()
        ))
    
    # 设置文件权限
    os.chmod(key_path, 0o600)
    
    logging.info(f"生成SSH服务器密钥: {key_path}")


def generate_ssl_cert(cert_path: str, key_path: str, hostname: str = "localhost"):
    """
    生成自签名SSL证书
    
    Args:
        cert_path: 证书文件路径
        key_path: 私钥文件路径
        hostname: 主机名
    """
    if os.path.exists(cert_path) and os.path.exists(key_path):
        return
        
    # 确保目录存在
    cert_dir = os.path.dirname(cert_path)
    if cert_dir:
        ensure_directory(cert_dir)
    
    # 生成私钥
    private_key = rsa.generate_private_key(
        public_exponent=65537,
        key_size=2048,
    )
    
    # 创建证书
    subject = issuer = x509.Name([
        x509.NameAttribute(NameOID.COUNTRY_NAME, "CN"),
        x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Beijing"),
        x509.NameAttribute(NameOID.LOCALITY_NAME, "Beijing"),
        x509.NameAttribute(NameOID.ORGANIZATION_NAME, "OMS Gateway"),
        x509.NameAttribute(NameOID.COMMON_NAME, hostname),
    ])
    
    cert = x509.CertificateBuilder().subject_name(
        subject
    ).issuer_name(
        issuer
    ).public_key(
        private_key.public_key()
    ).serial_number(
        x509.random_serial_number()
    ).not_valid_before(
        datetime.datetime.utcnow()
    ).not_valid_after(
        datetime.datetime.utcnow() + datetime.timedelta(days=365)
    ).add_extension(
        x509.SubjectAlternativeName([
            x509.DNSName(hostname),
            x509.DNSName("localhost"),
            x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
        ]),
        critical=False,
    ).sign(private_key, hashes.SHA256())
    
    # 保存私钥
    with open(key_path, 'wb') as f:
        f.write(private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()
        ))
    
    # 保存证书
    with open(cert_path, 'wb') as f:
        f.write(cert.public_bytes(serialization.Encoding.PEM))
    
    # 设置文件权限
    os.chmod(key_path, 0o600)
    os.chmod(cert_path, 0o644)
    
    logging.info(f"生成SSL证书: {cert_path}, {key_path}")


def create_authorized_keys_file(authorized_keys_path: str, public_key_content: str = None):
    """
    创建SSH授权密钥文件
    
    Args:
        authorized_keys_path: 授权密钥文件路径
        public_key_content: 公钥内容（可选）
    """
    if os.path.exists(authorized_keys_path):
        return
        
    # 确保目录存在
    keys_dir = os.path.dirname(authorized_keys_path)
    if keys_dir:
        ensure_directory(keys_dir)
    
    # 如果没有提供公钥内容，创建一个示例文件
    if not public_key_content:
        public_key_content = "# 请将您的SSH公钥添加到此文件中\n# 格式: ssh-rsa AAAAB3NzaC1yc2E... user@host\n"
    
    with open(authorized_keys_path, 'w', encoding='utf-8') as f:
        f.write(public_key_content)
    
    # 设置文件权限
    os.chmod(authorized_keys_path, 0o600)
    
    logging.info(f"创建SSH授权密钥文件: {authorized_keys_path}")


def safe_decode(data: bytes, encoding: str = 'utf-8', errors: str = 'replace') -> str:
    """
    安全解码字节数据为字符串
    
    Args:
        data: 字节数据
        encoding: 编码格式
        errors: 错误处理策略
        
    Returns:
        str: 解码后的字符串
    """
    try:
        return data.decode(encoding, errors=errors)
    except Exception:
        return data.decode('utf-8', errors='replace')


def safe_encode(text: str, encoding: str = 'utf-8', errors: str = 'replace') -> bytes:
    """
    安全编码字符串为字节数据
    
    Args:
        text: 字符串
        encoding: 编码格式
        errors: 错误处理策略
        
    Returns:
        bytes: 编码后的字节数据
    """
    try:
        return text.encode(encoding, errors=errors)
    except Exception:
        return text.encode('utf-8', errors='replace')
