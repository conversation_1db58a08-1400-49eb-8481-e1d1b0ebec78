"""
数据转发和协议转换测试模块
"""

import asyncio
import json
import unittest
from unittest.mock import AsyncMock, MagicMock, patch
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gateway.data_transfer import DataTransfer, TerminalDataProcessor


class TestDataTransfer(unittest.TestCase):
    """数据转发器测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.ssh_process = MagicMock()
        self.websocket = AsyncMock()
        self.device_id = "device_test_001"
        self.data_transfer = DataTransfer(
            ssh_process=self.ssh_process,
            websocket=self.websocket,
            device_id=self.device_id
        )
        
    def tearDown(self):
        """测试后清理"""
        asyncio.run(self._cleanup())
        
    async def _cleanup(self):
        """清理资源"""
        try:
            await self.data_transfer.stop()
        except:
            pass
            
    async def test_send_to_websocket_bytes(self):
        """测试发送字节数据到WebSocket"""
        test_data = b"hello world"
        
        await self.data_transfer._send_to_websocket("data", test_data)
        
        # 检查是否调用了WebSocket的send方法
        self.websocket.send.assert_called_once()
        
        # 检查发送的消息格式
        sent_message = self.websocket.send.call_args[0][0]
        parsed_message = json.loads(sent_message)
        
        self.assertEqual(parsed_message["type"], "data")
        self.assertEqual(parsed_message["payload"], "hello world")
        
    async def test_send_to_websocket_string(self):
        """测试发送字符串数据到WebSocket"""
        test_data = "hello world"
        
        await self.data_transfer._send_to_websocket("data", test_data)
        
        self.websocket.send.assert_called_once()
        
        sent_message = self.websocket.send.call_args[0][0]
        parsed_message = json.loads(sent_message)
        
        self.assertEqual(parsed_message["type"], "data")
        self.assertEqual(parsed_message["payload"], "hello world")
        
    async def test_send_to_ssh(self):
        """测试发送数据到SSH进程"""
        test_data = "ls -l\n"
        
        await self.data_transfer._send_to_ssh(test_data)
        
        # 检查是否调用了SSH进程的write_data方法
        self.ssh_process.write_data.assert_called_once()
        
        # 检查发送的数据是否正确编码
        sent_data = self.ssh_process.write_data.call_args[0][0]
        self.assertEqual(sent_data, test_data.encode('utf-8'))
        
    async def test_handle_resize(self):
        """测试处理终端大小调整"""
        resize_data = {"rows": 30, "cols": 120}
        
        await self.data_transfer._handle_resize(resize_data)
        
        # 检查是否调用了SSH进程的terminal_size_changed方法
        self.ssh_process.terminal_size_changed.assert_called_once_with(120, 30, 0, 0)
        
    async def test_handle_terminal_resize(self):
        """测试处理来自SSH的终端大小调整"""
        rows, cols = 25, 100
        
        await self.data_transfer.handle_terminal_resize(rows, cols)
        
        # 检查是否发送了resize消息到WebSocket
        self.websocket.send.assert_called_once()
        
        sent_message = self.websocket.send.call_args[0][0]
        parsed_message = json.loads(sent_message)
        
        self.assertEqual(parsed_message["type"], "resize")
        self.assertEqual(parsed_message["payload"]["rows"], rows)
        self.assertEqual(parsed_message["payload"]["cols"], cols)
        
    async def test_receive_from_websocket(self):
        """测试从WebSocket接收消息"""
        test_message = {"type": "data", "payload": "test command"}
        self.websocket.recv.return_value = json.dumps(test_message)
        
        received_message = await self.data_transfer._receive_from_websocket()
        
        self.assertEqual(received_message, test_message)
        
    async def test_receive_from_websocket_invalid_json(self):
        """测试从WebSocket接收无效JSON"""
        self.websocket.recv.return_value = "invalid json"
        
        received_message = await self.data_transfer._receive_from_websocket()
        
        self.assertIsNone(received_message)
        
    async def test_send_ssh_data_to_websocket(self):
        """测试将SSH数据发送到WebSocket"""
        test_data = b"command output\n"
        
        # 设置运行状态
        self.data_transfer._running = True
        
        await self.data_transfer.send_ssh_data_to_websocket(test_data)
        
        # 检查是否发送了数据到WebSocket
        self.websocket.send.assert_called_once()
        
        sent_message = self.websocket.send.call_args[0][0]
        parsed_message = json.loads(sent_message)
        
        self.assertEqual(parsed_message["type"], "data")
        self.assertEqual(parsed_message["payload"], "command output\n")
        
    async def test_send_ssh_data_to_websocket_not_running(self):
        """测试在未运行状态下发送SSH数据"""
        test_data = b"command output\n"
        
        # 设置未运行状态
        self.data_transfer._running = False
        
        await self.data_transfer.send_ssh_data_to_websocket(test_data)
        
        # 不应该发送数据
        self.websocket.send.assert_not_called()


class TestTerminalDataProcessor(unittest.TestCase):
    """终端数据处理器测试类"""
    
    def test_process_terminal_output_valid_utf8(self):
        """测试处理有效UTF-8终端输出"""
        test_data = "Hello, 世界!\n".encode('utf-8')
        
        result = TerminalDataProcessor.process_terminal_output(test_data)
        
        self.assertEqual(result, "Hello, 世界!\n")
        
    def test_process_terminal_output_invalid_bytes(self):
        """测试处理包含无效字节的终端输出"""
        # 创建包含无效UTF-8字节的数据
        test_data = b"Hello\xff\xfe World"
        
        result = TerminalDataProcessor.process_terminal_output(test_data)
        
        # 应该包含替换字符
        self.assertIn("Hello", result)
        self.assertIn("World", result)
        
    def test_process_terminal_input(self):
        """测试处理终端输入"""
        test_text = "ls -l\n"
        
        result = TerminalDataProcessor.process_terminal_input(test_text)
        
        self.assertEqual(result, test_text.encode('utf-8'))
        
    def test_is_control_sequence(self):
        """测试检查控制序列"""
        # 包含ANSI转义序列的数据
        ansi_data = b"\x1b[31mRed text\x1b[0m"
        self.assertTrue(TerminalDataProcessor.is_control_sequence(ansi_data))
        
        # 普通文本数据
        normal_data = b"Normal text"
        self.assertFalse(TerminalDataProcessor.is_control_sequence(normal_data))
        
        # 包含其他控制序列的数据
        control_data = b"\x1b(B"
        self.assertTrue(TerminalDataProcessor.is_control_sequence(control_data))
        
    def test_extract_printable_text(self):
        """测试提取可打印文本"""
        # 包含ANSI转义序列的数据
        ansi_data = b"\x1b[31mRed text\x1b[0m normal text"
        
        result = TerminalDataProcessor.extract_printable_text(ansi_data)
        
        # 应该移除ANSI转义序列
        self.assertEqual(result, "Red text normal text")
        
    def test_extract_printable_text_complex(self):
        """测试提取复杂的可打印文本"""
        # 复杂的ANSI转义序列
        complex_data = b"\x1b[2J\x1b[H\x1b[31;1mError:\x1b[0m File not found"
        
        result = TerminalDataProcessor.extract_printable_text(complex_data)
        
        # 应该只保留可打印文本
        self.assertIn("Error:", result)
        self.assertIn("File not found", result)
        self.assertNotIn("\x1b", result)


class TestDataTransferIntegration(unittest.TestCase):
    """数据转发集成测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.ssh_process = MagicMock()
        self.websocket = AsyncMock()
        self.device_id = "device_integration_test"
        self.data_transfer = DataTransfer(
            ssh_process=self.ssh_process,
            websocket=self.websocket,
            device_id=self.device_id
        )
        
    def tearDown(self):
        """测试后清理"""
        asyncio.run(self._cleanup())
        
    async def _cleanup(self):
        """清理资源"""
        try:
            await self.data_transfer.stop()
        except:
            pass
            
    async def test_websocket_to_ssh_data_flow(self):
        """测试WebSocket到SSH的数据流"""
        # 模拟WebSocket接收到的消息
        test_messages = [
            {"type": "data", "payload": "ls -l\n"},
            {"type": "resize", "payload": {"rows": 30, "cols": 120}},
            {"type": "data", "payload": "pwd\n"}
        ]
        
        # 设置WebSocket接收消息的模拟
        message_queue = [json.dumps(msg) for msg in test_messages]
        self.websocket.recv.side_effect = message_queue + [asyncio.CancelledError()]
        
        # 启动WebSocket到SSH的转发任务
        try:
            await self.data_transfer._websocket_to_ssh()
        except asyncio.CancelledError:
            pass
            
        # 检查SSH进程是否收到了数据
        self.ssh_process.write_data.assert_called()
        
        # 检查终端大小调整是否被处理
        self.ssh_process.terminal_size_changed.assert_called_with(120, 30, 0, 0)


# 异步测试运行器
class AsyncTestCase(unittest.TestCase):
    """异步测试基类"""
    
    def run_async(self, coro):
        """运行异步测试"""
        return asyncio.run(coro)


class TestDataTransferAsync(AsyncTestCase):
    """异步数据转发测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.ssh_process = MagicMock()
        self.websocket = AsyncMock()
        self.device_id = "device_async_test"
        self.data_transfer = DataTransfer(
            ssh_process=self.ssh_process,
            websocket=self.websocket,
            device_id=self.device_id
        )
        
    def tearDown(self):
        """测试后清理"""
        self.run_async(self.data_transfer.stop())
        
    def test_send_to_websocket_async(self):
        """测试异步发送到WebSocket"""
        async def test():
            await self.data_transfer._send_to_websocket("data", "test")
            self.websocket.send.assert_called_once()
            
        self.run_async(test())
        
    def test_send_to_ssh_async(self):
        """测试异步发送到SSH"""
        async def test():
            await self.data_transfer._send_to_ssh("test command")
            self.ssh_process.write_data.assert_called_once()
            
        self.run_async(test())


if __name__ == '__main__':
    unittest.main()
