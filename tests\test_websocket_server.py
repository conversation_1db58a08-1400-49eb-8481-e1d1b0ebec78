"""
WebSocket服务端测试模块
"""

import asyncio
import json
import unittest
from unittest.mock import AsyncMock, MagicMock, patch
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gateway.websocket_server import WebSocketServer, WebSocketClient
from gateway.session_manager import SessionManager


class TestWebSocketServer(unittest.TestCase):
    """WebSocket服务端测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.session_manager = SessionManager()
        self.config = {
            'websocket': {
                'host': '127.0.0.1',
                'port': 8765,
                'ssl_enabled': False
            }
        }
        self.ws_server = WebSocketServer(self.session_manager, self.config)
        
    def tearDown(self):
        """测试后清理"""
        asyncio.run(self._cleanup())
        
    async def _cleanup(self):
        """清理资源"""
        try:
            await self.ws_server.stop()
            await self.session_manager.stop()
        except:
            pass
            
    def test_extract_device_id(self):
        """测试设备ID提取"""
        test_cases = [
            ("/device_A100", "device_A100"),
            ("/device/device_B200", "device_B200"),
            ("/device_test_123", "device_test_123"),
            ("/device/", None),
            ("/", None),
            ("", None),
            ("/invalid/path/structure", None),
        ]
        
        for path, expected in test_cases:
            result = self.ws_server._extract_device_id(path)
            self.assertEqual(result, expected, f"路径 {path} 应该提取出 {expected}")
            
    async def test_send_message(self):
        """测试发送消息"""
        websocket = AsyncMock()
        message = {
            "type": "test",
            "payload": {"data": "hello"}
        }
        
        await self.ws_server._send_message(websocket, message)
        
        # 检查是否调用了send方法
        websocket.send.assert_called_once()
        
        # 检查发送的消息格式
        sent_message = websocket.send.call_args[0][0]
        parsed_message = json.loads(sent_message)
        self.assertEqual(parsed_message, message)
        
    async def test_send_error(self):
        """测试发送错误消息"""
        websocket = AsyncMock()
        error_message = "测试错误"
        
        await self.ws_server._send_error(websocket, error_message)
        
        # 检查是否发送了错误消息
        websocket.send.assert_called_once()
        
        # 检查是否关闭了连接
        websocket.close.assert_called_once()
        
    async def test_handle_message_data(self):
        """测试处理数据消息"""
        websocket = AsyncMock()
        device_id = "device_test_001"
        message = json.dumps({
            "type": "data",
            "payload": "test data"
        })
        
        await self.ws_server._handle_message(websocket, device_id, message)
        
        # 数据消息应该被正常处理（不抛出异常）
        
    async def test_handle_message_heartbeat(self):
        """测试处理心跳消息"""
        websocket = AsyncMock()
        device_id = "device_test_002"
        timestamp = 1234567890
        message = json.dumps({
            "type": "heartbeat",
            "payload": {"timestamp": timestamp}
        })
        
        await self.ws_server._handle_message(websocket, device_id, message)
        
        # 检查是否发送了心跳确认
        websocket.send.assert_called_once()
        
        sent_message = websocket.send.call_args[0][0]
        parsed_message = json.loads(sent_message)
        
        self.assertEqual(parsed_message["type"], "heartbeat_ack")
        self.assertEqual(parsed_message["payload"]["timestamp"], timestamp)
        
    async def test_handle_message_invalid_json(self):
        """测试处理无效JSON消息"""
        websocket = AsyncMock()
        device_id = "device_test_003"
        invalid_message = "invalid json"
        
        await self.ws_server._handle_message(websocket, device_id, invalid_message)
        
        # 应该发送错误消息
        websocket.send.assert_called_once()
        
        sent_message = websocket.send.call_args[0][0]
        parsed_message = json.loads(sent_message)
        
        self.assertEqual(parsed_message["type"], "error")
        self.assertIn("消息格式错误", parsed_message["payload"]["message"])


class TestWebSocketClient(unittest.TestCase):
    """WebSocket客户端测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.uri = "ws://localhost:8765/device_test"
        self.client = WebSocketClient(self.uri)
        
    def tearDown(self):
        """测试后清理"""
        asyncio.run(self._cleanup())
        
    async def _cleanup(self):
        """清理资源"""
        try:
            await self.client.disconnect()
        except:
            pass
            
    async def test_send_message(self):
        """测试发送消息"""
        # 模拟WebSocket连接
        mock_websocket = AsyncMock()
        self.client.websocket = mock_websocket
        
        message = {
            "type": "test",
            "payload": "test data"
        }
        
        await self.client.send_message(message)
        
        # 检查是否调用了send方法
        mock_websocket.send.assert_called_once()
        
        sent_message = mock_websocket.send.call_args[0][0]
        parsed_message = json.loads(sent_message)
        self.assertEqual(parsed_message, message)
        
    async def test_send_message_without_connection(self):
        """测试在未连接状态下发送消息"""
        message = {"type": "test", "payload": "test data"}
        
        with self.assertRaises(RuntimeError):
            await self.client.send_message(message)
            
    async def test_receive_message(self):
        """测试接收消息"""
        # 模拟WebSocket连接
        mock_websocket = AsyncMock()
        test_message = {"type": "test", "payload": "test data"}
        mock_websocket.recv.return_value = json.dumps(test_message)
        self.client.websocket = mock_websocket
        
        received_message = await self.client.receive_message()
        
        self.assertEqual(received_message, test_message)
        
    async def test_receive_message_without_connection(self):
        """测试在未连接状态下接收消息"""
        with self.assertRaises(RuntimeError):
            await self.client.receive_message()


class TestWebSocketIntegration(unittest.TestCase):
    """WebSocket集成测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.session_manager = SessionManager()
        self.config = {
            'websocket': {
                'host': '127.0.0.1',
                'port': 8766,  # 使用不同端口避免冲突
                'ssl_enabled': False
            }
        }
        self.ws_server = WebSocketServer(self.session_manager, self.config)
        
    def tearDown(self):
        """测试后清理"""
        asyncio.run(self._cleanup())
        
    async def _cleanup(self):
        """清理资源"""
        try:
            await self.ws_server.stop()
            await self.session_manager.stop()
        except:
            pass
            
    async def test_client_connection_flow(self):
        """测试客户端连接流程"""
        # 这个测试需要实际启动WebSocket服务器
        # 在实际环境中可能需要更复杂的设置
        
        # 启动会话管理器
        await self.session_manager.start()
        
        # 模拟设备注册
        device_id = "device_integration_test"
        mock_websocket = AsyncMock()
        
        # 验证设备ID格式
        self.assertTrue(self.session_manager.validate_device_id(device_id))
        
        # 注册设备
        result = await self.session_manager.register_device(device_id, mock_websocket)
        self.assertTrue(result)
        
        # 检查设备是否在线
        is_online = await self.session_manager.is_device_online(device_id)
        self.assertTrue(is_online)


# 异步测试运行器
class AsyncTestCase(unittest.TestCase):
    """异步测试基类"""
    
    def run_async(self, coro):
        """运行异步测试"""
        return asyncio.run(coro)


class TestWebSocketServerAsync(AsyncTestCase):
    """异步WebSocket服务端测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.session_manager = SessionManager()
        self.config = {
            'websocket': {
                'host': '127.0.0.1',
                'port': 8767,
                'ssl_enabled': False
            }
        }
        self.ws_server = WebSocketServer(self.session_manager, self.config)
        
    def tearDown(self):
        """测试后清理"""
        async def cleanup():
            await self.ws_server.stop()
            await self.session_manager.stop()
        self.run_async(cleanup())
        
    def test_send_message_async(self):
        """测试异步发送消息"""
        async def test():
            websocket = AsyncMock()
            message = {"type": "test", "payload": "data"}
            await self.ws_server._send_message(websocket, message)
            websocket.send.assert_called_once()
            
        self.run_async(test())
        
    def test_handle_message_async(self):
        """测试异步处理消息"""
        async def test():
            websocket = AsyncMock()
            device_id = "device_async_test"
            message = json.dumps({"type": "heartbeat", "payload": {"timestamp": 123}})
            await self.ws_server._handle_message(websocket, device_id, message)
            websocket.send.assert_called_once()
            
        self.run_async(test())


if __name__ == '__main__':
    unittest.main()
