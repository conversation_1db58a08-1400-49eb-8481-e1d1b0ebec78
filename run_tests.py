#!/usr/bin/env python3
"""
测试运行脚本

运行所有测试用例并生成测试报告。
"""

import sys
import os
import unittest
import logging
from io import StringIO

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 禁用日志输出以避免测试时的干扰
logging.disable(logging.CRITICAL)


def discover_and_run_tests():
    """发现并运行所有测试"""
    # 测试发现器
    loader = unittest.TestLoader()
    
    # 发现tests目录下的所有测试
    test_suite = loader.discover('tests', pattern='test_*.py')
    
    # 创建测试运行器
    stream = StringIO()
    runner = unittest.TextTestRunner(
        stream=stream,
        verbosity=2,
        buffer=True
    )
    
    # 运行测试
    print("开始运行测试...")
    print("=" * 60)
    
    result = runner.run(test_suite)
    
    # 输出测试结果
    output = stream.getvalue()
    print(output)
    
    # 输出测试统计
    print("=" * 60)
    print("测试统计:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    print(f"跳过数: {len(result.skipped)}")
    
    # 输出失败和错误详情
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
            
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    # 计算成功率
    if result.testsRun > 0:
        success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
        print(f"\n成功率: {success_rate:.1f}%")
    
    return result.wasSuccessful()


def run_specific_test(test_module):
    """运行特定的测试模块"""
    try:
        # 导入测试模块
        module = __import__(f'tests.{test_module}', fromlist=[test_module])
        
        # 创建测试套件
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(module)
        
        # 运行测试
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        return result.wasSuccessful()
        
    except ImportError as e:
        print(f"无法导入测试模块 {test_module}: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 运行特定测试
        test_module = sys.argv[1]
        if test_module.startswith('test_'):
            test_module = test_module[5:]  # 移除 'test_' 前缀
        if test_module.endswith('.py'):
            test_module = test_module[:-3]  # 移除 '.py' 后缀
            
        print(f"运行测试模块: test_{test_module}")
        success = run_specific_test(f'test_{test_module}')
    else:
        # 运行所有测试
        success = discover_and_run_tests()
    
    # 根据测试结果设置退出码
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
