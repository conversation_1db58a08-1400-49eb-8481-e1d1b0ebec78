"""
SSH服务端模块

基于asyncssh实现SSH服务端，处理堡垒机连接、认证、命令解析和Pty支持。
"""

import asyncio
import logging
import asyncssh
from typing import Optional, Dict, Any
from .session_manager import SessionManager
from .data_transfer import DataTransfer

logger = logging.getLogger(__name__)


class SSHServer:
    """SSH服务端类"""
    
    def __init__(self, session_manager: SessionManager, config: Dict[str, Any]):
        """
        初始化SSH服务端
        
        Args:
            session_manager: 会话管理器实例
            config: SSH服务配置
        """
        self.session_manager = session_manager
        self.config = config
        self.server = None
        
    async def start(self):
        """启动SSH服务"""
        ssh_config = self.config.get('ssh', {})
        host = ssh_config.get('host', '0.0.0.0')
        port = ssh_config.get('port', 2222)
        server_key_path = ssh_config.get('server_key_path', './keys/ssh_host_key')
        
        try:
            self.server = await asyncssh.listen(
                host=host,
                port=port,
                server_host_keys=[server_key_path],
                server_factory=lambda: SSHServerSession(self.session_manager),
                process_factory=SSHServerProcess,
                encoding=None,  # 处理原始字节数据
                line_editor=False,  # 禁用行编辑器以支持原始终端
            )
            logger.info(f"SSH服务启动成功，监听 {host}:{port}")
        except Exception as e:
            logger.error(f"SSH服务启动失败: {e}")
            raise
            
    async def stop(self):
        """停止SSH服务"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            logger.info("SSH服务已停止")


class SSHServerSession(asyncssh.SSHServer):
    """SSH服务端会话类"""
    
    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.config = session_manager.config if hasattr(session_manager, 'config') else {}
        
    def connection_made(self, conn):
        """连接建立时调用"""
        self._conn = conn
        logger.info(f"SSH连接建立: {conn.get_extra_info('peername')}")
        
    def connection_lost(self, exc):
        """连接断开时调用"""
        if exc:
            logger.error(f"SSH连接异常断开: {exc}")
        else:
            logger.info("SSH连接正常断开")
            
    def begin_auth(self, username):
        """开始认证过程"""
        logger.info(f"用户 {username} 开始SSH认证")
        return True
        
    def password_auth_supported(self):
        """是否支持密码认证"""
        return self.session_manager.config.get('ssh', {}).get('password_auth', False)
        
    def public_key_auth_supported(self):
        """是否支持公钥认证"""
        return True
        
    def validate_password(self, username, password):
        """验证密码"""
        # 简单的密码验证，实际应用中应该使用更安全的方式
        if username == "operator" and password == "password":
            logger.info(f"用户 {username} 密码认证成功")
            return True
        logger.warning(f"用户 {username} 密码认证失败")
        return False
        
    def validate_public_key(self, username, key):
        """验证公钥"""
        try:
            # 读取授权密钥文件
            authorized_keys_path = self.session_manager.config.get('ssh', {}).get(
                'authorized_keys_path', './keys/authorized_keys'
            )
            
            with open(authorized_keys_path, 'r') as f:
                authorized_keys = f.read()
                
            # 简单的公钥验证（实际应用中应该使用asyncssh的内置功能）
            key_string = key.export_public_key().decode()
            if key_string.strip() in authorized_keys:
                logger.info(f"用户 {username} 公钥认证成功")
                return True
                
        except Exception as e:
            logger.error(f"公钥认证时出错: {e}")
            
        logger.warning(f"用户 {username} 公钥认证失败")
        return False


class SSHServerProcess(asyncssh.SSHServerProcess):
    """SSH服务端进程类"""
    
    def __init__(self, process):
        self._process = process
        self._session_manager = process.channel.get_extra_info('session').session_manager
        self._device_id = None
        self._data_transfer = None
        self._websocket = None
        
    def connection_made(self, chan):
        """连接建立时调用"""
        self._chan = chan
        logger.debug("SSH进程连接建立")
        
    def connection_lost(self, exc):
        """连接断开时调用"""
        if exc:
            logger.debug(f"SSH进程连接异常断开: {exc}")
        else:
            logger.debug("SSH进程连接正常断开")
        if self._data_transfer:
            asyncio.create_task(self._data_transfer.stop())
        
    async def _handle_command(self, command):
        """处理SSH命令"""
        try:
            # 解析命令参数，获取device_id
            args = command.strip().split()
            if not args:
                await self._send_error("错误: 请指定设备ID")
                return
                
            self._device_id = args[0]
            
            # 验证设备ID格式
            if not self._session_manager.validate_device_id(self._device_id):
                await self._send_error(f"错误: 设备ID格式无效: {self._device_id}")
                return
                
            # 查找对应的WebSocket连接
            self._websocket = await self._session_manager.get_websocket(self._device_id)
            if not self._websocket:
                await self._send_error(f"错误: 设备 '{self._device_id}' 未在线")
                return
                
            # 创建数据转发器
            self._data_transfer = DataTransfer(
                ssh_process=self,
                websocket=self._websocket,
                device_id=self._device_id
            )
            
            # 注册SSH会话
            transfer_task = asyncio.create_task(self._data_transfer.start())
            await self._session_manager.register_ssh_session(self._device_id, transfer_task)
            
            logger.info(f"SSH会话建立成功，设备: {self._device_id}")
            
            # 等待数据转发任务完成
            try:
                await transfer_task
            except asyncio.CancelledError:
                logger.info(f"SSH会话被取消，设备: {self._device_id}")
            except Exception as e:
                logger.error(f"数据转发出错: {e}")
            finally:
                await self._session_manager.unregister_ssh_session(self._device_id)
                
        except Exception as e:
            logger.error(f"处理SSH命令时出错: {e}")
            await self._send_error(f"内部错误: {e}")
            
    async def _send_error(self, message):
        """发送错误消息"""
        try:
            self._chan.write(f"{message}\r\n")
            await self._chan.wait_closed()
        except Exception as e:
            logger.error(f"发送错误消息失败: {e}")
            
    def shell_requested(self):
        """处理shell请求"""
        # 对于我们的用例，不支持直接shell，需要指定设备ID
        return False
        
    def exec_requested(self, command):
        """处理exec请求"""
        # 创建任务处理命令
        asyncio.create_task(self._handle_command(command))
        return True
        
    def subsystem_requested(self, subsystem):
        """处理子系统请求"""
        _ = subsystem  # 忽略未使用的参数
        return False

    def pty_requested(self, term_type, term_size, term_modes):
        """处理PTY请求"""
        _ = term_modes  # 忽略未使用的参数
        logger.debug(f"PTY请求: {term_type}, 大小: {term_size}")
        return True

    def terminal_size_changed(self, width, height, pixwidth, pixheight):
        """处理终端大小变化"""
        _ = pixwidth, pixheight  # 忽略未使用的参数
        if self._data_transfer:
            asyncio.create_task(
                self._data_transfer.handle_terminal_resize(height, width)
            )
            
    def write_data(self, data):
        """写入数据到SSH通道"""
        try:
            self._chan.write(data)
        except Exception as e:
            logger.error(f"写入SSH数据失败: {e}")
            
    def close(self):
        """关闭SSH进程"""
        try:
            self._chan.close()
        except Exception as e:
            logger.error(f"关闭SSH进程失败: {e}")
