#!/usr/bin/env python3
"""
SSH客户端演示程序

演示如何通过SSH连接到网关并与设备通信。
"""

import asyncio
import asyncssh
import sys
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SSHClientDemo:
    """SSH客户端演示类"""
    
    def __init__(self, host: str, port: int, username: str, device_id: str):
        """
        初始化SSH客户端
        
        Args:
            host: SSH服务器地址
            port: SSH服务器端口
            username: SSH用户名
            device_id: 目标设备ID
        """
        self.host = host
        self.port = port
        self.username = username
        self.device_id = device_id
        self.connection = None
        
    async def connect(self, password: str = None):
        """连接到SSH服务器"""
        try:
            logger.info(f"连接到SSH服务器: {self.host}:{self.port}")
            
            # 连接参数
            connect_kwargs = {
                'host': self.host,
                'port': self.port,
                'username': self.username,
                'known_hosts': None,  # 忽略主机密钥验证（仅用于演示）
            }
            
            if password:
                connect_kwargs['password'] = password
                
            self.connection = await asyncssh.connect(**connect_kwargs)
            logger.info("SSH连接建立成功")
            
        except Exception as e:
            logger.error(f"SSH连接失败: {e}")
            raise
            
    async def disconnect(self):
        """断开SSH连接"""
        if self.connection:
            self.connection.close()
            await self.connection.wait_closed()
            logger.info("SSH连接已断开")
            
    async def execute_command(self, command: str):
        """执行SSH命令"""
        if not self.connection:
            raise RuntimeError("SSH未连接")
            
        try:
            logger.info(f"执行命令: {command}")
            
            # 执行命令，指定设备ID
            full_command = f"{self.device_id}"
            result = await self.connection.run(full_command, input=command)
            
            if result.stdout:
                print("命令输出:")
                print(result.stdout)
                
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
                
            return result.exit_status
            
        except Exception as e:
            logger.error(f"执行命令失败: {e}")
            raise
            
    async def interactive_session(self):
        """交互式会话"""
        if not self.connection:
            raise RuntimeError("SSH未连接")
            
        try:
            logger.info(f"启动交互式会话，目标设备: {self.device_id}")
            
            # 创建交互式进程
            process = await self.connection.create_process(self.device_id)
            
            print(f"已连接到设备 {self.device_id}")
            print("输入命令（输入 'exit' 退出）:")
            
            # 启动输出读取任务
            output_task = asyncio.create_task(self._read_output(process))
            
            try:
                while True:
                    # 读取用户输入
                    try:
                        command = await asyncio.get_event_loop().run_in_executor(
                            None, input, "$ "
                        )
                    except EOFError:
                        break
                        
                    if command.strip().lower() == 'exit':
                        break
                        
                    # 发送命令
                    process.stdin.write(command + '\n')
                    await process.stdin.drain()
                    
            except KeyboardInterrupt:
                print("\n会话被用户中断")
            finally:
                # 清理
                output_task.cancel()
                process.terminate()
                await process.wait()
                
        except Exception as e:
            logger.error(f"交互式会话失败: {e}")
            raise
            
    async def _read_output(self, process):
        """读取进程输出"""
        try:
            while True:
                data = await process.stdout.read(1024)
                if not data:
                    break
                print(data.decode('utf-8', errors='replace'), end='')
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"读取输出失败: {e}")


async def demo_basic_commands():
    """演示基本命令执行"""
    print("\n=== 基本命令演示 ===")
    
    client = SSHClientDemo(
        host='localhost',
        port=2222,
        username='operator',
        device_id='device_A100'
    )
    
    try:
        await client.connect(password='password')
        
        # 执行一些基本命令
        commands = ['ls -l', 'pwd', 'whoami', 'echo "Hello World"']
        
        for command in commands:
            print(f"\n执行命令: {command}")
            await client.execute_command(command)
            
    finally:
        await client.disconnect()


async def demo_interactive_session():
    """演示交互式会话"""
    print("\n=== 交互式会话演示 ===")
    
    client = SSHClientDemo(
        host='localhost',
        port=2222,
        username='operator',
        device_id='device_A100'
    )
    
    try:
        await client.connect(password='password')
        await client.interactive_session()
        
    finally:
        await client.disconnect()


async def main():
    """主函数"""
    print("=" * 60)
    print("SSH客户端演示程序")
    print("=" * 60)
    print("此程序演示如何通过SSH连接到网关并与设备通信")
    print()
    print("前提条件:")
    print("1. 网关服务已启动 (python main.py)")
    print("2. 有设备连接到网关 (python examples/websocket_client_demo.py)")
    print()
    
    if len(sys.argv) > 1 and sys.argv[1] == 'interactive':
        await demo_interactive_session()
    else:
        await demo_basic_commands()
        
        print("\n提示: 运行 'python ssh_client_demo.py interactive' 体验交互式会话")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
