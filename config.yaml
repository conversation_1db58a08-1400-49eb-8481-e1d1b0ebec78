# 堡垒机到微信小程序通信网关配置文件

# SSH服务配置
ssh:
  host: 0.0.0.0
  port: 2222
  # SSH服务器密钥路径（如果不存在会自动生成）
  server_key_path: ./keys/ssh_host_key
  # 授权公钥文件路径
  authorized_keys_path: ./keys/authorized_keys
  # 是否启用密码认证（建议使用公钥认证）
  password_auth: false
  # 默认用户名（用于简化认证）
  default_user: operator

# WebSocket服务配置
websocket:
  host: 0.0.0.0
  port: 8765
  # SSL证书路径（用于WSS）
  ssl_cert_path: ./certs/cert.pem
  ssl_key_path: ./certs/key.pem
  # 是否启用SSL（生产环境建议启用）
  ssl_enabled: false

# 日志配置
log:
  level: INFO
  file: ./logs/gateway.log
  # 是否同时输出到控制台
  console: true
  # 日志轮转配置
  max_size: 10MB
  backup_count: 5

# 会话管理配置
session:
  # 设备连接超时时间（秒）
  device_timeout: 300
  # SSH会话超时时间（秒）
  ssh_timeout: 3600
  # 心跳检测间隔（秒）
  heartbeat_interval: 30

# 安全配置
security:
  # 是否启用设备ID验证
  device_id_validation: true
  # 允许的设备ID模式（正则表达式）
  device_id_pattern: "^device_[A-Za-z0-9_]+$"
  # 最大并发连接数
  max_connections: 100
