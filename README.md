# 堡垒机到微信小程序通信网关

基于Python asyncio的云端通信网关，实现堡垒机到微信小程序（及其背后的工控机）的全功能Shell交互。

## 项目概述

本项目实现了一个完整的通信网关系统，基于技术方案文档《堡垒机到微信小程序通信网关方案.md》中的设计要求。该网关作为协议转换和会话路由的枢纽，在堡垒机和微信小程序之间建立透明的数据通道。

## 系统架构

```
+-----------+       (SSH)       +---------------------+       (WSS)       +-------------------+       (BLE)       +-------------+
|           |                   |  云端通信网关        |                   |                   |                   |             |
| 堡垒机     | <---------------> | (Python/Asyncio)    | <---------------> |  微信小程序        | <---------------> |  工控机      |
|(JumpServer)|                   |  - AsyncSSH Server  |                   | (WS Client)       |   (BT-Agent)    | (Pty)       |
|           |-- SSH a@gw device_A100 ->|  - Websockets Server|                   |                   |                   |             |
|           |                   |  - Session Manager  |-- wss://gw/device_A100 --<|                   |                   |             |
+-----------+                   +---------------------+                   +-------------------+                   +-------------+
```

### 核心组件

1. **SSH服务端** - 基于asyncssh，处理堡垒机连接
2. **WebSocket服务端** - 基于websockets，处理微信小程序连接
3. **会话管理器** - 管理设备注册和会话路由
4. **数据转发器** - 处理SSH和WebSocket之间的数据转换

## 功能特性

- ✅ SSH全功能终端（Pty）支持
- ✅ 终端窗口大小调整
- ✅ 设备在线状态管理
- ✅ 多设备并发连接
- ✅ JSON格式数据传输
- ✅ 公钥/密码认证
- ✅ SSL/TLS加密支持
- ✅ 会话超时管理
- ✅ 心跳检测机制
- ✅ 完整的错误处理
- ✅ 详细的日志记录

## 快速开始

### 环境要求

- Python 3.7+
- pip包管理器
- Linux/Windows/macOS

### 安装依赖

```bash
# 克隆项目
git clone <repository_url>
cd oms-gateway

# 安装依赖
pip install -r requirements.txt
```

### 快速启动

```bash
# 直接运行（使用默认配置）
python main.py

# 或使用启动脚本（Linux/macOS）
./scripts/start.sh
```

### 基本配置

编辑`config.yaml`文件：

```yaml
ssh:
  host: 0.0.0.0
  port: 2222                      # SSH服务端口
  password_auth: true             # 启用密码认证（开发环境）

websocket:
  host: 0.0.0.0
  port: 8765                      # WebSocket服务端口
  ssl_enabled: false              # 开发环境可关闭SSL

log:
  level: INFO
  console: true                   # 输出到控制台
```

## 使用方法

### 1. 设备端连接（微信小程序）

```javascript
// WebSocket连接
const ws = new WebSocket('ws://localhost:8765/device_A100');

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    if (message.type === 'data') {
        console.log('终端数据:', message.payload);
    }
};

// 发送命令响应
function sendResponse(data) {
    ws.send(JSON.stringify({
        type: 'data',
        payload: data
    }));
}
```

### 2. 运维端连接（堡垒机）

```bash
# SSH连接到指定设备
ssh operator@localhost -p 2222 device_A100

# 输入密码：password（默认）
# 连接成功后可以执行命令，输出会转发到微信小程序
```

## 部署指南

### 开发环境

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动服务
python main.py
```

### 生产环境

```bash
# 使用自动部署脚本（Linux）
sudo ./scripts/deploy.sh install

# 手动部署请参考 docs/deployment.md
```

## 项目结构

```
oms-gateway/
├── README.md                 # 项目说明
├── requirements.txt          # Python依赖
├── config.yaml              # 配置文件
├── main.py                  # 主程序入口
├── run_tests.py             # 测试运行器
├── gateway/                 # 核心模块
│   ├── __init__.py
│   ├── session_manager.py   # 会话管理器
│   ├── ssh_server.py        # SSH服务端
│   ├── websocket_server.py  # WebSocket服务端
│   ├── data_transfer.py     # 数据转发器
│   ├── config.py            # 配置管理
│   └── utils.py             # 工具函数
├── tests/                   # 测试用例
│   ├── test_session_manager.py
│   ├── test_websocket_server.py
│   └── test_data_transfer.py
├── scripts/                 # 部署脚本
│   ├── start.sh             # 启动脚本
│   └── deploy.sh            # 部署脚本
└── docs/                    # 文档
    ├── API.md               # API文档
    └── deployment.md        # 部署指南
```

## 测试

```bash
# 运行所有测试
python run_tests.py

# 运行特定测试模块
python run_tests.py session_manager
python run_tests.py websocket_server
python run_tests.py data_transfer
```

## 文档

- [API文档](docs/API.md) - 详细的接口说明
- [部署指南](docs/deployment.md) - 生产环境部署
- [技术方案](堡垒机到微信小程序通信网关方案.md) - 原始设计文档

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License
