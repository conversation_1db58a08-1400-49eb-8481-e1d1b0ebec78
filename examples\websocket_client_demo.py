#!/usr/bin/env python3
"""
WebSocket客户端演示程序

模拟微信小程序连接到网关的行为。
"""

import asyncio
import json
import websockets
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockDevice:
    """模拟设备类"""
    
    def __init__(self, device_id: str, gateway_uri: str):
        """
        初始化模拟设备
        
        Args:
            device_id: 设备ID
            gateway_uri: 网关WebSocket URI
        """
        self.device_id = device_id
        self.gateway_uri = gateway_uri
        self.websocket = None
        self.running = False
        
    async def connect(self):
        """连接到网关"""
        try:
            uri = f"{self.gateway_uri}/{self.device_id}"
            logger.info(f"连接到网关: {uri}")
            
            self.websocket = await websockets.connect(uri)
            self.running = True
            
            logger.info(f"设备 {self.device_id} 连接成功")
            
        except Exception as e:
            logger.error(f"连接失败: {e}")
            raise
            
    async def disconnect(self):
        """断开连接"""
        self.running = False
        if self.websocket:
            await self.websocket.close()
            logger.info(f"设备 {self.device_id} 已断开连接")
            
    async def send_message(self, msg_type: str, payload):
        """发送消息到网关"""
        if not self.websocket:
            raise RuntimeError("WebSocket未连接")
            
        message = {
            "type": msg_type,
            "payload": payload
        }
        
        await self.websocket.send(json.dumps(message, ensure_ascii=False))
        logger.debug(f"发送消息: {msg_type}")
        
    async def send_heartbeat(self):
        """发送心跳"""
        import time
        await self.send_message("heartbeat", {"timestamp": int(time.time())})
        
    async def send_command_response(self, response: str):
        """发送命令响应"""
        await self.send_message("data", response)
        
    async def handle_message(self, message_data: dict):
        """处理收到的消息"""
        msg_type = message_data.get("type")
        payload = message_data.get("payload")
        
        logger.info(f"收到消息: {msg_type}")
        
        if msg_type == "connected":
            logger.info(f"连接确认: {payload}")
            
        elif msg_type == "data":
            # 模拟处理终端命令
            command = payload.strip()
            logger.info(f"收到命令: {command}")
            
            # 模拟命令执行结果
            if command == "ls -l":
                response = "total 0\ndrwxr-xr-x 1 <USER> <GROUP> 4096 Jan 1 12:00 test_dir\n-rw-r--r-- 1 <USER> <GROUP> 1024 Jan 1 12:00 test_file.txt\n"
            elif command == "pwd":
                response = "/home/<USER>"
            elif command == "whoami":
                response = "root\n"
            elif command.startswith("echo"):
                response = command[5:] + "\n"
            else:
                response = f"bash: {command}: command not found\n"
                
            # 发送响应
            await self.send_command_response(response)
            
        elif msg_type == "resize":
            rows = payload.get("rows", 24)
            cols = payload.get("cols", 80)
            logger.info(f"终端大小调整: {rows}x{cols}")
            
        elif msg_type == "error":
            logger.error(f"收到错误: {payload.get('message')}")
            
    async def message_loop(self):
        """消息处理循环"""
        try:
            async for message in self.websocket:
                try:
                    data = json.loads(message)
                    await self.handle_message(data)
                except json.JSONDecodeError as e:
                    logger.error(f"JSON解析错误: {e}")
                except Exception as e:
                    logger.error(f"处理消息时出错: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket连接关闭")
        except Exception as e:
            logger.error(f"消息循环出错: {e}")
        finally:
            self.running = False
            
    async def heartbeat_loop(self):
        """心跳循环"""
        while self.running:
            try:
                await self.send_heartbeat()
                await asyncio.sleep(30)  # 每30秒发送一次心跳
            except Exception as e:
                logger.error(f"心跳发送失败: {e}")
                break
                
    async def run(self):
        """运行设备"""
        await self.connect()
        
        try:
            # 启动消息处理和心跳任务
            message_task = asyncio.create_task(self.message_loop())
            heartbeat_task = asyncio.create_task(self.heartbeat_loop())
            
            # 等待任一任务完成
            done, pending = await asyncio.wait(
                [message_task, heartbeat_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # 取消剩余任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                    
        finally:
            await self.disconnect()


async def main():
    """主函数"""
    # 配置
    device_id = "device_A100"
    gateway_uri = "ws://localhost:8765"
    
    print("=" * 60)
    print("WebSocket客户端演示程序")
    print("=" * 60)
    print(f"设备ID: {device_id}")
    print(f"网关地址: {gateway_uri}")
    print()
    print("使用说明:")
    print("1. 确保网关服务已启动")
    print("2. 运行此程序模拟设备连接")
    print("3. 使用SSH客户端连接到网关测试通信")
    print("   ssh operator@localhost -p 2222 device_A100")
    print()
    print("按 Ctrl+C 停止程序")
    print("=" * 60)
    
    # 创建模拟设备
    device = MockDevice(device_id, gateway_uri)
    
    try:
        await device.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == "__main__":
    asyncio.run(main())
