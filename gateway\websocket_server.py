"""
WebSocket服务端模块

基于websockets实现WSS服务，处理微信小程序连接和JSON消息格式。
"""

import asyncio
import json
import logging
import ssl
import websockets
from websockets.server import WebSocketServerProtocol
from urllib.parse import urlparse
from typing import Dict, Any, Optional
from .session_manager import SessionManager

logger = logging.getLogger(__name__)


class WebSocketServer:
    """WebSocket服务端类"""
    
    def __init__(self, session_manager: SessionManager, config: Dict[str, Any]):
        """
        初始化WebSocket服务端
        
        Args:
            session_manager: 会话管理器实例
            config: WebSocket服务配置
        """
        self.session_manager = session_manager
        self.config = config
        self.server = None
        
    async def start(self):
        """启动WebSocket服务"""
        ws_config = self.config.get('websocket', {})
        host = ws_config.get('host', '0.0.0.0')
        port = ws_config.get('port', 8765)
        ssl_enabled = ws_config.get('ssl_enabled', False)
        
        # SSL配置
        ssl_context = None
        if ssl_enabled:
            ssl_cert_path = ws_config.get('ssl_cert_path', './certs/cert.pem')
            ssl_key_path = ws_config.get('ssl_key_path', './certs/key.pem')
            
            ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
            ssl_context.load_cert_chain(ssl_cert_path, ssl_key_path)
            
        try:
            self.server = await websockets.serve(
                self._handle_client,
                host,
                port,
                ssl=ssl_context,
                ping_interval=30,  # 30秒心跳
                ping_timeout=10,   # 10秒超时
                close_timeout=10,  # 10秒关闭超时
            )
            
            protocol = "wss" if ssl_enabled else "ws"
            logger.info(f"WebSocket服务启动成功，监听 {protocol}://{host}:{port}")
            
        except Exception as e:
            logger.error(f"WebSocket服务启动失败: {e}")
            raise
            
    async def stop(self):
        """停止WebSocket服务"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            logger.info("WebSocket服务已停止")
            
    async def _handle_client(self, websocket: WebSocketServerProtocol, path: str):
        """处理客户端连接"""
        device_id = None
        client_info = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"
        
        try:
            # 从URL路径中提取device_id
            device_id = self._extract_device_id(path)
            if not device_id:
                await self._send_error(websocket, "无效的连接路径，请指定设备ID")
                return
                
            logger.info(f"WebSocket客户端连接: {client_info}, 设备ID: {device_id}")
            
            # 验证设备ID格式
            if not self.session_manager.validate_device_id(device_id):
                await self._send_error(websocket, f"设备ID格式无效: {device_id}")
                return
                
            # 注册设备
            success = await self.session_manager.register_device(device_id, websocket)
            if not success:
                await self._send_error(websocket, f"设备注册失败: {device_id}")
                return
                
            # 发送连接成功消息
            await self._send_message(websocket, {
                "type": "connected",
                "payload": {"device_id": device_id, "status": "online"}
            })
            
            # 处理消息循环
            await self._message_loop(websocket, device_id)
            
        except websockets.exceptions.ConnectionClosed:
            logger.info(f"WebSocket客户端断开连接: {client_info}")
        except Exception as e:
            logger.error(f"处理WebSocket客户端时出错: {e}")
        finally:
            # 注销设备
            if device_id:
                await self.session_manager.unregister_device(device_id)
                logger.info(f"设备 {device_id} 已注销")
                
    def _extract_device_id(self, path: str) -> Optional[str]:
        """
        从URL路径中提取设备ID

        Args:
            path: URL路径，格式为 /<device_id> 或 /device/<device_id>

        Returns:
            Optional[str]: 设备ID，如果提取失败则返回None
        """
        try:
            # 移除开头的斜杠并分割路径
            path_parts = path.strip('/').split('/')

            # 过滤空字符串
            path_parts = [part for part in path_parts if part]

            if len(path_parts) == 1:
                # 格式: /<device_id>
                device_id = path_parts[0]
                # 确保不是 'device' 这个关键字
                return device_id if device_id != 'device' else None
            elif len(path_parts) == 2 and path_parts[0] == 'device':
                # 格式: /device/<device_id>
                return path_parts[1]
            else:
                return None

        except Exception as e:
            logger.error(f"提取设备ID时出错: {e}")
            return None
            
    async def _message_loop(self, websocket: WebSocketServerProtocol, device_id: str):
        """消息处理循环"""
        try:
            async for message in websocket:
                await self._handle_message(websocket, device_id, message)
        except websockets.exceptions.ConnectionClosed:
            logger.debug(f"设备 {device_id} WebSocket连接关闭")
        except Exception as e:
            logger.error(f"消息循环出错: {e}")
            
    async def _handle_message(self, websocket: WebSocketServerProtocol, device_id: str, message: str):
        """处理收到的消息"""
        try:
            # 解析JSON消息
            data = json.loads(message)
            msg_type = data.get('type')
            payload = data.get('payload')
            
            logger.debug(f"收到设备 {device_id} 消息: {msg_type}")
            
            if msg_type == 'data':
                # 终端数据，转发给对应的SSH会话
                # 这里的逻辑将在DataTransfer模块中处理
                pass
            elif msg_type == 'heartbeat':
                # 心跳消息
                await self._send_message(websocket, {
                    "type": "heartbeat_ack",
                    "payload": {"timestamp": payload.get('timestamp')}
                })
            elif msg_type == 'status':
                # 状态消息
                logger.info(f"设备 {device_id} 状态: {payload}")
            else:
                logger.warning(f"未知消息类型: {msg_type}")
                
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析错误: {e}")
            await self._send_error(websocket, "消息格式错误")
        except Exception as e:
            logger.error(f"处理消息时出错: {e}")
            
    async def _send_message(self, websocket: WebSocketServerProtocol, message: Dict[str, Any]):
        """发送消息给客户端"""
        try:
            json_message = json.dumps(message, ensure_ascii=False)
            await websocket.send(json_message)
            logger.debug(f"发送消息: {message.get('type')}")
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            
    async def _send_error(self, websocket: WebSocketServerProtocol, error_message: str):
        """发送错误消息给客户端"""
        try:
            await self._send_message(websocket, {
                "type": "error",
                "payload": {"message": error_message}
            })
            # 发送错误后关闭连接
            await websocket.close(code=1000, reason=error_message)
        except Exception as e:
            logger.error(f"发送错误消息失败: {e}")


class WebSocketClient:
    """WebSocket客户端辅助类（用于测试）"""
    
    def __init__(self, uri: str):
        """
        初始化WebSocket客户端
        
        Args:
            uri: WebSocket服务器URI
        """
        self.uri = uri
        self.websocket = None
        
    async def connect(self):
        """连接到WebSocket服务器"""
        try:
            self.websocket = await websockets.connect(self.uri)
            logger.info(f"连接到WebSocket服务器: {self.uri}")
        except Exception as e:
            logger.error(f"连接WebSocket服务器失败: {e}")
            raise
            
    async def disconnect(self):
        """断开WebSocket连接"""
        if self.websocket:
            await self.websocket.close()
            logger.info("WebSocket连接已断开")
            
    async def send_message(self, message: Dict[str, Any]):
        """发送消息"""
        if not self.websocket:
            raise RuntimeError("WebSocket未连接")
            
        try:
            json_message = json.dumps(message, ensure_ascii=False)
            await self.websocket.send(json_message)
            logger.debug(f"发送消息: {message.get('type')}")
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            raise
            
    async def receive_message(self) -> Dict[str, Any]:
        """接收消息"""
        if not self.websocket:
            raise RuntimeError("WebSocket未连接")
            
        try:
            message = await self.websocket.recv()
            data = json.loads(message)
            logger.debug(f"收到消息: {data.get('type')}")
            return data
        except Exception as e:
            logger.error(f"接收消息失败: {e}")
            raise
            
    async def message_loop(self, message_handler):
        """消息处理循环"""
        if not self.websocket:
            raise RuntimeError("WebSocket未连接")
            
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await message_handler(data)
        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket连接关闭")
        except Exception as e:
            logger.error(f"消息循环出错: {e}")
            raise
