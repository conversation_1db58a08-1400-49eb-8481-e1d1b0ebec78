"""
会话管理器测试模块
"""

import asyncio
import unittest
from unittest.mock import AsyncMock, MagicMock
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gateway.session_manager import SessionManager


class TestSessionManager(unittest.TestCase):
    """会话管理器测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.session_manager = SessionManager(
            device_timeout=60,
            device_id_pattern=r"^device_[A-Za-z0-9_]+$"
        )
        
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'session_manager'):
            # 清理会话管理器
            asyncio.run(self.session_manager.stop())
            
    def test_validate_device_id(self):
        """测试设备ID验证"""
        # 有效的设备ID
        valid_ids = [
            "device_A100",
            "device_test_123",
            "device_ABC_xyz_789"
        ]
        
        for device_id in valid_ids:
            self.assertTrue(
                self.session_manager.validate_device_id(device_id),
                f"设备ID应该有效: {device_id}"
            )
            
        # 无效的设备ID
        invalid_ids = [
            "invalid_id",
            "device-123",
            "device 123",
            "device_",
            "",
            "device_123!",
            "DEVICE_123"
        ]
        
        for device_id in invalid_ids:
            self.assertFalse(
                self.session_manager.validate_device_id(device_id),
                f"设备ID应该无效: {device_id}"
            )
            
    async def test_register_device(self):
        """测试设备注册"""
        device_id = "device_test_001"
        websocket = AsyncMock()
        
        # 启动会话管理器
        await self.session_manager.start()
        
        # 注册设备
        result = await self.session_manager.register_device(device_id, websocket)
        self.assertTrue(result, "设备注册应该成功")
        
        # 检查设备是否在线
        is_online = await self.session_manager.is_device_online(device_id)
        self.assertTrue(is_online, "设备应该在线")
        
        # 获取WebSocket连接
        ws = await self.session_manager.get_websocket(device_id)
        self.assertEqual(ws, websocket, "应该返回正确的WebSocket连接")
        
    async def test_register_invalid_device(self):
        """测试注册无效设备ID"""
        device_id = "invalid_id"
        websocket = AsyncMock()
        
        await self.session_manager.start()
        
        # 注册无效设备ID
        result = await self.session_manager.register_device(device_id, websocket)
        self.assertFalse(result, "无效设备ID注册应该失败")
        
    async def test_unregister_device(self):
        """测试设备注销"""
        device_id = "device_test_002"
        websocket = AsyncMock()
        
        await self.session_manager.start()
        
        # 先注册设备
        await self.session_manager.register_device(device_id, websocket)
        
        # 注销设备
        result = await self.session_manager.unregister_device(device_id)
        self.assertTrue(result, "设备注销应该成功")
        
        # 检查设备是否离线
        is_online = await self.session_manager.is_device_online(device_id)
        self.assertFalse(is_online, "设备应该离线")
        
        # 获取WebSocket连接应该返回None
        ws = await self.session_manager.get_websocket(device_id)
        self.assertIsNone(ws, "应该返回None")
        
    async def test_unregister_nonexistent_device(self):
        """测试注销不存在的设备"""
        device_id = "device_nonexistent"
        
        await self.session_manager.start()
        
        # 注销不存在的设备
        result = await self.session_manager.unregister_device(device_id)
        self.assertFalse(result, "注销不存在的设备应该失败")
        
    async def test_get_online_devices(self):
        """测试获取在线设备列表"""
        devices = ["device_test_003", "device_test_004", "device_test_005"]
        websockets = [AsyncMock() for _ in devices]
        
        await self.session_manager.start()
        
        # 注册多个设备
        for device_id, websocket in zip(devices, websockets):
            await self.session_manager.register_device(device_id, websocket)
            
        # 获取在线设备列表
        online_devices = await self.session_manager.get_online_devices()
        self.assertEqual(len(online_devices), len(devices), "在线设备数量应该正确")
        
        for device_id in devices:
            self.assertIn(device_id, online_devices, f"设备 {device_id} 应该在线")
            
    async def test_ssh_session_management(self):
        """测试SSH会话管理"""
        device_id = "device_test_006"
        ssh_task = AsyncMock()
        
        await self.session_manager.start()
        
        # 注册SSH会话
        await self.session_manager.register_ssh_session(device_id, ssh_task)
        
        # 检查SSH会话是否注册
        self.assertIn(device_id, self.session_manager._ssh_sessions)
        
        # 注销SSH会话
        await self.session_manager.unregister_ssh_session(device_id)
        
        # 检查SSH会话是否注销
        self.assertNotIn(device_id, self.session_manager._ssh_sessions)
        
    async def test_replace_existing_device(self):
        """测试替换已存在的设备连接"""
        device_id = "device_test_007"
        old_websocket = AsyncMock()
        new_websocket = AsyncMock()
        
        await self.session_manager.start()
        
        # 注册设备
        await self.session_manager.register_device(device_id, old_websocket)
        
        # 用新连接替换旧连接
        result = await self.session_manager.register_device(device_id, new_websocket)
        self.assertTrue(result, "替换设备连接应该成功")
        
        # 检查旧连接是否被关闭
        old_websocket.close.assert_called_once()
        
        # 检查新连接是否生效
        ws = await self.session_manager.get_websocket(device_id)
        self.assertEqual(ws, new_websocket, "应该返回新的WebSocket连接")
        
    def test_get_stats(self):
        """测试获取统计信息"""
        stats = self.session_manager.get_stats()
        
        self.assertIsInstance(stats, dict, "统计信息应该是字典")
        self.assertIn('online_devices', stats, "应该包含在线设备数")
        self.assertIn('active_ssh_sessions', stats, "应该包含活跃SSH会话数")
        self.assertIn('device_timeout', stats, "应该包含设备超时时间")


# 异步测试运行器
class AsyncTestCase(unittest.TestCase):
    """异步测试基类"""
    
    def run_async(self, coro):
        """运行异步测试"""
        return asyncio.run(coro)


class TestSessionManagerAsync(AsyncTestCase):
    """异步会话管理器测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.session_manager = SessionManager(
            device_timeout=60,
            device_id_pattern=r"^device_[A-Za-z0-9_]+$"
        )
        
    def tearDown(self):
        """测试后清理"""
        self.run_async(self.session_manager.stop())
        
    def test_register_device_async(self):
        """测试异步设备注册"""
        async def test():
            device_id = "device_async_001"
            websocket = AsyncMock()
            
            await self.session_manager.start()
            result = await self.session_manager.register_device(device_id, websocket)
            self.assertTrue(result)
            
        self.run_async(test())
        
    def test_unregister_device_async(self):
        """测试异步设备注销"""
        async def test():
            device_id = "device_async_002"
            websocket = AsyncMock()
            
            await self.session_manager.start()
            await self.session_manager.register_device(device_id, websocket)
            result = await self.session_manager.unregister_device(device_id)
            self.assertTrue(result)
            
        self.run_async(test())


if __name__ == '__main__':
    unittest.main()
