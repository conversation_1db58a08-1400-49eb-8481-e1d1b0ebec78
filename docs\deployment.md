# 部署指南

## 系统要求

### 硬件要求

- CPU: 2核心以上
- 内存: 4GB以上
- 存储: 20GB以上可用空间
- 网络: 稳定的互联网连接

### 软件要求

- 操作系统: Linux (Ubuntu 18.04+, CentOS 7+) 或 Windows 10+
- Python: 3.7或更高版本
- pip: Python包管理器

### 网络要求

- SSH端口（默认2222）需要对外开放
- WebSocket端口（默认8765）需要对外开放
- 如果启用SSL，需要有效的SSL证书

## 安装方式

### 方式一：自动部署（推荐）

#### Linux系统

```bash
# 下载项目
git clone <repository_url>
cd oms-gateway

# 运行部署脚本（需要root权限）
sudo ./scripts/deploy.sh install
```

#### Windows系统

```powershell
# 下载项目
git clone <repository_url>
cd oms-gateway

# 安装依赖
pip install -r requirements.txt

# 运行服务
python main.py
```

### 方式二：手动部署

#### 1. 创建服务用户

```bash
# 创建专用用户
sudo useradd -r -s /bin/false -d /opt/oms-gateway oms-gateway
```

#### 2. 安装系统依赖

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install -y python3 python3-pip python3-venv git
```

**CentOS/RHEL:**
```bash
sudo yum update -y
sudo yum install -y python3 python3-pip git
```

#### 3. 复制项目文件

```bash
# 创建安装目录
sudo mkdir -p /opt/oms-gateway

# 复制文件
sudo cp -r ./* /opt/oms-gateway/

# 设置权限
sudo chown -R oms-gateway:oms-gateway /opt/oms-gateway
```

#### 4. 安装Python依赖

```bash
cd /opt/oms-gateway

# 创建虚拟环境
sudo -u oms-gateway python3 -m venv venv

# 安装依赖
sudo -u oms-gateway bash -c "source venv/bin/activate && pip install -r requirements.txt"
```

#### 5. 配置systemd服务

创建服务文件 `/etc/systemd/system/oms-gateway.service`:

```ini
[Unit]
Description=OMS Gateway Service
After=network.target

[Service]
Type=simple
User=oms-gateway
Group=oms-gateway
WorkingDirectory=/opt/oms-gateway
Environment=PATH=/opt/oms-gateway/venv/bin
ExecStart=/opt/oms-gateway/venv/bin/python /opt/oms-gateway/main.py
Restart=always
RestartSec=10

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/oms-gateway

[Install]
WantedBy=multi-user.target
```

#### 6. 启动服务

```bash
# 重新加载systemd
sudo systemctl daemon-reload

# 启用并启动服务
sudo systemctl enable oms-gateway
sudo systemctl start oms-gateway

# 检查服务状态
sudo systemctl status oms-gateway
```

## 配置说明

### 主配置文件

编辑 `config.yaml` 文件：

```yaml
# SSH服务配置
ssh:
  host: 0.0.0.0                    # 监听地址
  port: 2222                       # SSH端口
  server_key_path: ./keys/ssh_host_key
  authorized_keys_path: ./keys/authorized_keys
  password_auth: false             # 是否启用密码认证
  default_user: operator           # 默认用户名

# WebSocket服务配置
websocket:
  host: 0.0.0.0                    # 监听地址
  port: 8765                       # WebSocket端口
  ssl_cert_path: ./certs/cert.pem  # SSL证书路径
  ssl_key_path: ./certs/key.pem    # SSL私钥路径
  ssl_enabled: false               # 是否启用SSL

# 日志配置
log:
  level: INFO                      # 日志级别
  file: ./logs/gateway.log         # 日志文件路径
  console: true                    # 是否输出到控制台
  max_size: 10MB                   # 日志文件最大大小
  backup_count: 5                  # 日志文件备份数量

# 会话管理配置
session:
  device_timeout: 300              # 设备连接超时时间（秒）
  ssh_timeout: 3600                # SSH会话超时时间（秒）
  heartbeat_interval: 30           # 心跳检测间隔（秒）

# 安全配置
security:
  device_id_validation: true       # 是否启用设备ID验证
  device_id_pattern: "^device_[A-Za-z0-9_]+$"  # 设备ID正则表达式
  max_connections: 100             # 最大并发连接数
```

### SSH密钥配置

#### 生成SSH服务器密钥

```bash
# 创建密钥目录
mkdir -p keys

# 生成SSH服务器密钥
ssh-keygen -t rsa -b 2048 -f keys/ssh_host_key -N ""
```

#### 配置授权密钥

```bash
# 创建授权密钥文件
touch keys/authorized_keys
chmod 600 keys/authorized_keys

# 添加公钥（替换为实际的公钥）
echo "ssh-rsa AAAAB3NzaC1yc2E... user@host" >> keys/authorized_keys
```

### SSL证书配置

#### 生成自签名证书（开发环境）

```bash
# 创建证书目录
mkdir -p certs

# 生成私钥
openssl genrsa -out certs/key.pem 2048

# 生成证书
openssl req -new -x509 -key certs/key.pem -out certs/cert.pem -days 365 \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=OMS Gateway/CN=localhost"
```

#### 使用正式证书（生产环境）

```bash
# 复制证书文件
cp /path/to/your/certificate.pem certs/cert.pem
cp /path/to/your/private_key.pem certs/key.pem

# 设置权限
chmod 644 certs/cert.pem
chmod 600 certs/key.pem
```

## 防火墙配置

### Ubuntu (ufw)

```bash
# 开放SSH端口
sudo ufw allow 2222/tcp

# 开放WebSocket端口
sudo ufw allow 8765/tcp

# 启用防火墙
sudo ufw enable
```

### CentOS (firewalld)

```bash
# 开放端口
sudo firewall-cmd --permanent --add-port=2222/tcp
sudo firewall-cmd --permanent --add-port=8765/tcp

# 重新加载配置
sudo firewall-cmd --reload
```

### iptables

```bash
# 开放SSH端口
sudo iptables -A INPUT -p tcp --dport 2222 -j ACCEPT

# 开放WebSocket端口
sudo iptables -A INPUT -p tcp --dport 8765 -j ACCEPT

# 保存规则
sudo iptables-save > /etc/iptables/rules.v4
```

## 服务管理

### systemd命令

```bash
# 启动服务
sudo systemctl start oms-gateway

# 停止服务
sudo systemctl stop oms-gateway

# 重启服务
sudo systemctl restart oms-gateway

# 查看服务状态
sudo systemctl status oms-gateway

# 查看服务日志
sudo journalctl -u oms-gateway -f

# 启用开机自启
sudo systemctl enable oms-gateway

# 禁用开机自启
sudo systemctl disable oms-gateway
```

### 手动启动

```bash
cd /opt/oms-gateway

# 激活虚拟环境
source venv/bin/activate

# 启动服务
python main.py
```

## 监控和维护

### 日志监控

```bash
# 实时查看日志
tail -f /opt/oms-gateway/logs/gateway.log

# 查看系统日志
sudo journalctl -u oms-gateway -f

# 查看错误日志
sudo journalctl -u oms-gateway -p err
```

### 性能监控

```bash
# 查看进程状态
ps aux | grep python

# 查看端口占用
netstat -tuln | grep -E "(2222|8765)"

# 查看系统资源使用
top -p $(pgrep -f "python.*main.py")
```

### 备份和恢复

#### 备份

```bash
# 创建备份目录
mkdir -p /backup/oms-gateway

# 备份配置文件
cp /opt/oms-gateway/config.yaml /backup/oms-gateway/

# 备份密钥文件
cp -r /opt/oms-gateway/keys /backup/oms-gateway/

# 备份证书文件
cp -r /opt/oms-gateway/certs /backup/oms-gateway/

# 备份日志文件
cp -r /opt/oms-gateway/logs /backup/oms-gateway/
```

#### 恢复

```bash
# 停止服务
sudo systemctl stop oms-gateway

# 恢复配置文件
cp /backup/oms-gateway/config.yaml /opt/oms-gateway/

# 恢复密钥文件
cp -r /backup/oms-gateway/keys /opt/oms-gateway/

# 恢复证书文件
cp -r /backup/oms-gateway/certs /opt/oms-gateway/

# 设置权限
sudo chown -R oms-gateway:oms-gateway /opt/oms-gateway

# 启动服务
sudo systemctl start oms-gateway
```

## 故障排除

### 常见问题

#### 1. 服务启动失败

```bash
# 查看详细错误信息
sudo journalctl -u oms-gateway -n 50

# 检查配置文件语法
python -c "import yaml; yaml.safe_load(open('config.yaml'))"

# 检查端口占用
netstat -tuln | grep -E "(2222|8765)"
```

#### 2. SSH连接失败

```bash
# 检查SSH服务状态
sudo systemctl status oms-gateway

# 检查SSH密钥权限
ls -la keys/

# 测试SSH连接
ssh -v operator@localhost -p 2222 device_test
```

#### 3. WebSocket连接失败

```bash
# 检查WebSocket端口
telnet localhost 8765

# 检查SSL证书（如果启用）
openssl s_client -connect localhost:8765

# 查看WebSocket日志
grep -i websocket /opt/oms-gateway/logs/gateway.log
```

### 性能优化

#### 1. 系统优化

```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.somaxconn = 1024" >> /etc/sysctl.conf
sysctl -p
```

#### 2. 应用优化

- 调整配置文件中的超时时间
- 增加最大连接数限制
- 启用日志轮转以节省磁盘空间

## 卸载

### 自动卸载

```bash
sudo ./scripts/deploy.sh uninstall
```

### 手动卸载

```bash
# 停止并禁用服务
sudo systemctl stop oms-gateway
sudo systemctl disable oms-gateway

# 删除服务文件
sudo rm /etc/systemd/system/oms-gateway.service
sudo systemctl daemon-reload

# 删除安装目录
sudo rm -rf /opt/oms-gateway

# 删除用户
sudo userdel oms-gateway
```
