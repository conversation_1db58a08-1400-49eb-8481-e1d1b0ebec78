"""
会话管理器模块

负责管理设备会话，包括设备注册、注销和会话查询功能。
作为网关的大脑，维护一个全局的字典，映射device_id到活跃的websocket连接对象。
"""

import asyncio
import logging
import time
from typing import Dict, Optional, Set
from websockets.server import WebSocketServerProtocol
import re

logger = logging.getLogger(__name__)


class SessionManager:
    """会话管理器类"""
    
    def __init__(self, device_timeout: int = 300, device_id_pattern: str = r"^device_[A-Za-z0-9_]+$"):
        """
        初始化会话管理器
        
        Args:
            device_timeout: 设备连接超时时间（秒）
            device_id_pattern: 设备ID验证正则表达式
        """
        self._devices: Dict[str, WebSocketServerProtocol] = {}
        self._device_timestamps: Dict[str, float] = {}
        self._ssh_sessions: Dict[str, asyncio.Task] = {}
        self._device_timeout = device_timeout
        self._device_id_pattern = re.compile(device_id_pattern)
        self._lock = asyncio.Lock()
        
        # 启动清理任务
        self._cleanup_task = None
        
    async def start(self):
        """启动会话管理器"""
        logger.info("启动会话管理器")
        self._cleanup_task = asyncio.create_task(self._cleanup_expired_devices())
        
    async def stop(self):
        """停止会话管理器"""
        logger.info("停止会话管理器")
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
                
        # 关闭所有WebSocket连接
        async with self._lock:
            for device_id, websocket in self._devices.items():
                try:
                    await websocket.close()
                    logger.info(f"关闭设备 {device_id} 的WebSocket连接")
                except Exception as e:
                    logger.error(f"关闭设备 {device_id} 连接时出错: {e}")
            
            self._devices.clear()
            self._device_timestamps.clear()
            
    def validate_device_id(self, device_id: str) -> bool:
        """
        验证设备ID格式
        
        Args:
            device_id: 设备ID
            
        Returns:
            bool: 验证结果
        """
        return bool(self._device_id_pattern.match(device_id))
        
    async def register_device(self, device_id: str, websocket: WebSocketServerProtocol) -> bool:
        """
        注册设备
        
        Args:
            device_id: 设备ID
            websocket: WebSocket连接对象
            
        Returns:
            bool: 注册是否成功
        """
        if not self.validate_device_id(device_id):
            logger.warning(f"设备ID格式无效: {device_id}")
            return False
            
        async with self._lock:
            # 如果设备已存在，先关闭旧连接
            if device_id in self._devices:
                old_websocket = self._devices[device_id]
                try:
                    await old_websocket.close()
                    logger.info(f"关闭设备 {device_id} 的旧连接")
                except Exception as e:
                    logger.error(f"关闭设备 {device_id} 旧连接时出错: {e}")
                    
            self._devices[device_id] = websocket
            self._device_timestamps[device_id] = time.time()
            
        logger.info(f"设备 {device_id} 注册成功")
        return True
        
    async def unregister_device(self, device_id: str) -> bool:
        """
        注销设备
        
        Args:
            device_id: 设备ID
            
        Returns:
            bool: 注销是否成功
        """
        async with self._lock:
            if device_id in self._devices:
                del self._devices[device_id]
                del self._device_timestamps[device_id]
                
                # 如果有对应的SSH会话，也要取消
                if device_id in self._ssh_sessions:
                    ssh_task = self._ssh_sessions[device_id]
                    ssh_task.cancel()
                    del self._ssh_sessions[device_id]
                    
                logger.info(f"设备 {device_id} 注销成功")
                return True
            else:
                logger.warning(f"尝试注销不存在的设备: {device_id}")
                return False
                
    async def get_websocket(self, device_id: str) -> Optional[WebSocketServerProtocol]:
        """
        获取设备的WebSocket连接
        
        Args:
            device_id: 设备ID
            
        Returns:
            Optional[WebSocketServerProtocol]: WebSocket连接对象，如果设备不在线则返回None
        """
        async with self._lock:
            websocket = self._devices.get(device_id)
            if websocket:
                # 更新时间戳
                self._device_timestamps[device_id] = time.time()
                logger.debug(f"获取设备 {device_id} 的WebSocket连接")
                return websocket
            else:
                logger.debug(f"设备 {device_id} 不在线")
                return None
                
    async def is_device_online(self, device_id: str) -> bool:
        """
        检查设备是否在线
        
        Args:
            device_id: 设备ID
            
        Returns:
            bool: 设备是否在线
        """
        async with self._lock:
            return device_id in self._devices
            
    async def get_online_devices(self) -> Set[str]:
        """
        获取所有在线设备列表
        
        Returns:
            Set[str]: 在线设备ID集合
        """
        async with self._lock:
            return set(self._devices.keys())
            
    async def register_ssh_session(self, device_id: str, ssh_task: asyncio.Task):
        """
        注册SSH会话任务
        
        Args:
            device_id: 设备ID
            ssh_task: SSH会话任务
        """
        async with self._lock:
            self._ssh_sessions[device_id] = ssh_task
            logger.info(f"为设备 {device_id} 注册SSH会话")
            
    async def unregister_ssh_session(self, device_id: str):
        """
        注销SSH会话任务
        
        Args:
            device_id: 设备ID
        """
        async with self._lock:
            if device_id in self._ssh_sessions:
                del self._ssh_sessions[device_id]
                logger.info(f"注销设备 {device_id} 的SSH会话")
                
    async def _cleanup_expired_devices(self):
        """清理过期设备的后台任务"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                current_time = time.time()
                expired_devices = []
                
                async with self._lock:
                    for device_id, timestamp in self._device_timestamps.items():
                        if current_time - timestamp > self._device_timeout:
                            expired_devices.append(device_id)
                            
                # 清理过期设备
                for device_id in expired_devices:
                    logger.info(f"清理过期设备: {device_id}")
                    await self.unregister_device(device_id)
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"清理过期设备时出错: {e}")
                
    def get_stats(self) -> Dict:
        """
        获取会话管理器统计信息
        
        Returns:
            Dict: 统计信息
        """
        return {
            "online_devices": len(self._devices),
            "active_ssh_sessions": len(self._ssh_sessions),
            "device_timeout": self._device_timeout
        }
