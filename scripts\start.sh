#!/bin/bash

# 堡垒机到微信小程序通信网关启动脚本

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查Python版本
check_python() {
    log_info "检查Python版本..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
    log_info "Python版本: $PYTHON_VERSION"
    
    # 检查Python版本是否 >= 3.7
    if python3 -c "import sys; exit(0 if sys.version_info >= (3, 7) else 1)"; then
        log_info "Python版本检查通过"
    else
        log_error "需要Python 3.7或更高版本"
        exit 1
    fi
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    cd "$PROJECT_DIR"
    
    if [ ! -f "requirements.txt" ]; then
        log_error "requirements.txt 文件不存在"
        exit 1
    fi
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        log_warn "虚拟环境不存在，正在创建..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    log_info "安装依赖..."
    pip install -r requirements.txt
    
    log_info "依赖检查完成"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    cd "$PROJECT_DIR"
    
    if [ ! -f "config.yaml" ]; then
        log_warn "配置文件不存在，将使用默认配置"
    else
        log_info "配置文件存在"
    fi
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    cd "$PROJECT_DIR"
    
    mkdir -p keys
    mkdir -p certs
    mkdir -p logs
    
    log_info "目录创建完成"
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用..."
    
    # 默认端口
    SSH_PORT=2222
    WS_PORT=8765
    
    # 从配置文件读取端口（如果存在）
    if [ -f "config.yaml" ]; then
        SSH_PORT=$(python3 -c "
import yaml
try:
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    print(config.get('ssh', {}).get('port', 2222))
except:
    print(2222)
" 2>/dev/null || echo 2222)
        
        WS_PORT=$(python3 -c "
import yaml
try:
    with open('config.yaml', 'r') as f:
        config = yaml.safe_load(f)
    print(config.get('websocket', {}).get('port', 8765))
except:
    print(8765)
" 2>/dev/null || echo 8765)
    fi
    
    # 检查SSH端口
    if netstat -tuln 2>/dev/null | grep -q ":$SSH_PORT "; then
        log_warn "SSH端口 $SSH_PORT 已被占用"
    else
        log_info "SSH端口 $SSH_PORT 可用"
    fi
    
    # 检查WebSocket端口
    if netstat -tuln 2>/dev/null | grep -q ":$WS_PORT "; then
        log_warn "WebSocket端口 $WS_PORT 已被占用"
    else
        log_info "WebSocket端口 $WS_PORT 可用"
    fi
}

# 启动服务
start_service() {
    log_info "启动网关服务..."
    
    cd "$PROJECT_DIR"
    
    # 激活虚拟环境
    if [ -d "venv" ]; then
        source venv/bin/activate
    fi
    
    # 启动服务
    python3 main.py
}

# 主函数
main() {
    echo "========================================"
    echo "堡垒机到微信小程序通信网关启动脚本"
    echo "========================================"
    
    check_python
    check_dependencies
    check_config
    create_directories
    check_ports
    start_service
}

# 运行主函数
main "$@"
