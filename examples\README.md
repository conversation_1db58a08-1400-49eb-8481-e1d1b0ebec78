# 使用示例

本目录包含了堡垒机到微信小程序通信网关的使用示例。

## 示例文件

- `websocket_client_demo.py` - WebSocket客户端演示（模拟微信小程序）
- `ssh_client_demo.py` - SSH客户端演示（模拟堡垒机连接）

## 快速体验

### 1. 启动网关服务

```bash
# 在项目根目录下
python main.py
```

服务启动后会显示：
```
========================================
堡垒机到微信小程序通信网关
========================================
SSH服务地址: 0.0.0.0:2222
WebSocket服务地址: ws://0.0.0.0:8765

使用方法:
1. 设备端（微信小程序）连接:
   ws://0.0.0.0:8765/<device_id>
   例如: ws://localhost:8765/device_A100

2. 运维端（堡垒机）连接:
   ssh operator@0.0.0.0 -p 2222 <device_id>
   例如: ssh operator@localhost -p 2222 device_A100

按 Ctrl+C 停止服务
========================================
```

### 2. 启动模拟设备（终端1）

```bash
# 在examples目录下
python websocket_client_demo.py
```

输出示例：
```
============================================================
WebSocket客户端演示程序
============================================================
设备ID: device_A100
网关地址: ws://localhost:8765

使用说明:
1. 确保网关服务已启动
2. 运行此程序模拟设备连接
3. 使用SSH客户端连接到网关测试通信
   ssh operator@localhost -p 2222 device_A100

按 Ctrl+C 停止程序
============================================================
INFO:__main__:连接到网关: ws://localhost:8765/device_A100
INFO:__main__:设备 device_A100 连接成功
INFO:__main__:收到消息: connected
INFO:__main__:连接确认: {'device_id': 'device_A100', 'status': 'online'}
```

### 3. 连接SSH客户端（终端2）

#### 方式一：使用标准SSH客户端

```bash
ssh operator@localhost -p 2222 device_A100
# 密码: password
```

#### 方式二：使用演示程序

```bash
# 基本命令演示
python examples/ssh_client_demo.py

# 交互式会话演示
python examples/ssh_client_demo.py interactive
```

### 4. 测试通信

当SSH客户端连接成功后，可以执行各种命令：

```bash
$ ls -l
total 0
drwxr-xr-x 1 <USER> <GROUP> 4096 Jan 1 12:00 test_dir
-rw-r--r-- 1 <USER> <GROUP> 1024 Jan 1 12:00 test_file.txt

$ pwd
/home/<USER>

$ whoami
root

$ echo "Hello World"
Hello World
```

## 详细说明

### WebSocket客户端演示

`websocket_client_demo.py` 模拟了微信小程序的行为：

1. **连接到网关**: 通过WebSocket连接到指定的设备ID
2. **处理消息**: 接收来自SSH的命令并返回模拟的执行结果
3. **心跳机制**: 定期发送心跳保持连接
4. **命令模拟**: 对常见命令（ls, pwd, whoami, echo）返回模拟结果

### SSH客户端演示

`ssh_client_demo.py` 演示了如何通过SSH连接到网关：

1. **基本命令模式**: 执行预定义的命令并显示结果
2. **交互式模式**: 提供类似终端的交互体验
3. **错误处理**: 处理连接失败和命令执行错误

## 自定义配置

### 修改设备ID

在 `websocket_client_demo.py` 中修改：
```python
device_id = "device_YOUR_ID"  # 改为你的设备ID
```

在SSH连接时使用相同的设备ID：
```bash
ssh operator@localhost -p 2222 device_YOUR_ID
```

### 修改网关地址

如果网关运行在其他地址，修改相应的配置：

```python
# WebSocket客户端
gateway_uri = "ws://your-gateway-host:8765"

# SSH客户端
host = "your-gateway-host"
port = 2222
```

### 添加SSL支持

如果网关启用了SSL，修改WebSocket URI：
```python
gateway_uri = "wss://your-gateway-host:8765"  # 使用wss://
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 确保网关服务已启动
   - 检查端口是否被占用
   - 确认防火墙设置

2. **设备不在线**
   - 确保WebSocket客户端已连接
   - 检查设备ID是否匹配
   - 查看网关日志

3. **认证失败**
   - 检查SSH用户名和密码
   - 确认SSH配置正确

### 调试技巧

1. **查看网关日志**
   ```bash
   # 网关会输出详细的连接和数据传输日志
   ```

2. **启用详细日志**
   在演示程序中修改日志级别：
   ```python
   logging.basicConfig(level=logging.DEBUG)
   ```

3. **网络连接测试**
   ```bash
   # 测试SSH端口
   telnet localhost 2222
   
   # 测试WebSocket端口
   telnet localhost 8765
   ```

## 扩展示例

### 添加新的命令处理

在 `websocket_client_demo.py` 的 `handle_message` 方法中添加：

```python
elif command == "date":
    import datetime
    response = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S") + "\n"
elif command == "uptime":
    response = "up 1 day, 2:34, 1 user, load average: 0.1, 0.2, 0.3\n"
```

### 文件传输模拟

可以扩展演示程序支持文件传输命令：

```python
elif command.startswith("cat "):
    filename = command[4:].strip()
    response = f"Content of {filename}\nLine 1\nLine 2\n"
```

### 多设备支持

可以同时运行多个WebSocket客户端模拟多个设备：

```bash
# 终端1
python websocket_client_demo.py  # device_A100

# 修改device_id后在终端2运行
python websocket_client_demo.py  # device_B200
```

然后可以通过不同的设备ID连接：
```bash
ssh operator@localhost -p 2222 device_A100
ssh operator@localhost -p 2222 device_B200
```
