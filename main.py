#!/usr/bin/env python3
"""
堡垒机到微信小程序通信网关主程序

基于Python asyncio的云端通信网关，实现堡垒机到微信小程序（及其背后的工控机）的全功能Shell交互。
"""

import asyncio
import signal
import sys
import os
import logging
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gateway.config import config_manager
from gateway.session_manager import SessionManager
from gateway.ssh_server import SSHServer
from gateway.websocket_server import WebSocketServer
from gateway.utils import setup_logging, generate_ssh_key, generate_ssl_cert, create_authorized_keys_file, ensure_directory

logger = logging.getLogger(__name__)


class GatewayService:
    """网关服务主类"""
    
    def __init__(self):
        """初始化网关服务"""
        self.config = None
        self.session_manager: Optional[SessionManager] = None
        self.ssh_server: Optional[SSHServer] = None
        self.websocket_server: Optional[WebSocketServer] = None
        self._shutdown_event = asyncio.Event()
        
    async def initialize(self):
        """初始化服务"""
        try:
            # 加载配置
            self.config = config_manager.load_config()
            
            # 设置日志
            setup_logging(config_manager._config_to_dict(self.config))
            logger.info("网关服务初始化开始")
            
            # 确保必要的目录存在
            self._ensure_directories()
            
            # 生成必要的密钥和证书
            self._generate_keys_and_certs()
            
            # 初始化会话管理器
            self.session_manager = SessionManager(
                device_timeout=self.config.session.device_timeout,
                device_id_pattern=self.config.security.device_id_pattern
            )
            # 将配置传递给会话管理器
            self.session_manager.config = config_manager._config_to_dict(self.config)
            
            # 初始化SSH服务器
            self.ssh_server = SSHServer(
                session_manager=self.session_manager,
                config=config_manager._config_to_dict(self.config)
            )
            
            # 初始化WebSocket服务器
            self.websocket_server = WebSocketServer(
                session_manager=self.session_manager,
                config=config_manager._config_to_dict(self.config)
            )
            
            logger.info("网关服务初始化完成")
            
        except Exception as e:
            logger.error(f"网关服务初始化失败: {e}")
            raise
            
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            os.path.dirname(self.config.ssh.server_key_path),
            os.path.dirname(self.config.ssh.authorized_keys_path),
            os.path.dirname(self.config.websocket.ssl_cert_path),
            os.path.dirname(self.config.log.file),
        ]
        
        for directory in directories:
            if directory:
                ensure_directory(directory)
                
    def _generate_keys_and_certs(self):
        """生成必要的密钥和证书"""
        # 生成SSH服务器密钥
        generate_ssh_key(self.config.ssh.server_key_path)
        
        # 创建SSH授权密钥文件
        create_authorized_keys_file(self.config.ssh.authorized_keys_path)
        
        # 如果启用SSL，生成SSL证书
        if self.config.websocket.ssl_enabled:
            generate_ssl_cert(
                self.config.websocket.ssl_cert_path,
                self.config.websocket.ssl_key_path
            )
            
    async def start(self):
        """启动网关服务"""
        try:
            logger.info("启动网关服务")
            
            # 启动会话管理器
            await self.session_manager.start()
            
            # 启动SSH服务器
            await self.ssh_server.start()
            
            # 启动WebSocket服务器
            await self.websocket_server.start()
            
            logger.info("网关服务启动成功")
            
            # 打印服务信息
            self._print_service_info()
            
        except Exception as e:
            logger.error(f"启动网关服务失败: {e}")
            raise
            
    async def stop(self):
        """停止网关服务"""
        try:
            logger.info("停止网关服务")
            
            # 停止WebSocket服务器
            if self.websocket_server:
                await self.websocket_server.stop()
                
            # 停止SSH服务器
            if self.ssh_server:
                await self.ssh_server.stop()
                
            # 停止会话管理器
            if self.session_manager:
                await self.session_manager.stop()
                
            logger.info("网关服务已停止")
            
        except Exception as e:
            logger.error(f"停止网关服务时出错: {e}")
            
    async def run(self):
        """运行网关服务"""
        try:
            await self.initialize()
            await self.start()
            
            # 等待关闭信号
            await self._shutdown_event.wait()
            
        except KeyboardInterrupt:
            logger.info("收到键盘中断信号")
        except Exception as e:
            logger.error(f"运行网关服务时出错: {e}")
        finally:
            await self.stop()
            
    def shutdown(self):
        """触发关闭"""
        self._shutdown_event.set()
        
    def _print_service_info(self):
        """打印服务信息"""
        print("\n" + "="*60)
        print("堡垒机到微信小程序通信网关")
        print("="*60)
        print(f"SSH服务地址: {self.config.ssh.host}:{self.config.ssh.port}")
        
        ws_protocol = "wss" if self.config.websocket.ssl_enabled else "ws"
        print(f"WebSocket服务地址: {ws_protocol}://{self.config.websocket.host}:{self.config.websocket.port}")
        
        print("\n使用方法:")
        print("1. 设备端（微信小程序）连接:")
        print(f"   {ws_protocol}://{self.config.websocket.host}:{self.config.websocket.port}/<device_id>")
        print("   例如: ws://localhost:8765/device_A100")
        
        print("\n2. 运维端（堡垒机）连接:")
        print(f"   ssh {self.config.ssh.default_user}@{self.config.ssh.host} -p {self.config.ssh.port} <device_id>")
        print("   例如: ssh operator@localhost -p 2222 device_A100")
        
        print("\n按 Ctrl+C 停止服务")
        print("="*60 + "\n")


def setup_signal_handlers(gateway_service: GatewayService):
    """设置信号处理器"""
    def signal_handler(signum, frame):
        logger.info(f"收到信号 {signum}")
        gateway_service.shutdown()
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """主函数"""
    gateway_service = GatewayService()
    
    # 设置信号处理器
    setup_signal_handlers(gateway_service)
    
    try:
        await gateway_service.run()
    except Exception as e:
        logger.error(f"网关服务运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n网关服务已停止")
    except Exception as e:
        print(f"启动网关服务失败: {e}")
        sys.exit(1)
