"""
配置管理模块

提供配置文件加载、验证和管理功能。
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class SSHConfig:
    """SSH服务配置"""
    host: str = "0.0.0.0"
    port: int = 2222
    server_key_path: str = "./keys/ssh_host_key"
    authorized_keys_path: str = "./keys/authorized_keys"
    password_auth: bool = False
    default_user: str = "operator"


@dataclass
class WebSocketConfig:
    """WebSocket服务配置"""
    host: str = "0.0.0.0"
    port: int = 8765
    ssl_cert_path: str = "./certs/cert.pem"
    ssl_key_path: str = "./certs/key.pem"
    ssl_enabled: bool = False


@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    file: str = "./logs/gateway.log"
    console: bool = True
    max_size: str = "10MB"
    backup_count: int = 5


@dataclass
class SessionConfig:
    """会话管理配置"""
    device_timeout: int = 300
    ssh_timeout: int = 3600
    heartbeat_interval: int = 30


@dataclass
class SecurityConfig:
    """安全配置"""
    device_id_validation: bool = True
    device_id_pattern: str = r"^device_[A-Za-z0-9_]+$"
    max_connections: int = 100


@dataclass
class GatewayConfig:
    """网关总配置"""
    ssh: SSHConfig = field(default_factory=SSHConfig)
    websocket: WebSocketConfig = field(default_factory=WebSocketConfig)
    log: LogConfig = field(default_factory=LogConfig)
    session: SessionConfig = field(default_factory=SessionConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self._config: Optional[GatewayConfig] = None
        
    def load_config(self) -> GatewayConfig:
        """
        加载配置文件
        
        Returns:
            GatewayConfig: 网关配置对象
        """
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config_dict = yaml.safe_load(f)
                    
                if config_dict is None:
                    config_dict = {}
                    
                self._config = self._dict_to_config(config_dict)
                logger.info(f"配置文件加载成功: {self.config_path}")
            else:
                logger.warning(f"配置文件不存在，使用默认配置: {self.config_path}")
                self._config = GatewayConfig()
                
            # 验证配置
            self._validate_config()
            
            return self._config
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            logger.info("使用默认配置")
            self._config = GatewayConfig()
            return self._config
            
    def save_config(self, config: Optional[GatewayConfig] = None):
        """
        保存配置到文件
        
        Args:
            config: 要保存的配置对象，如果为None则保存当前配置
        """
        if config is None:
            config = self._config
            
        if config is None:
            raise ValueError("没有可保存的配置")
            
        try:
            # 确保配置目录存在
            config_dir = os.path.dirname(self.config_path)
            if config_dir and not os.path.exists(config_dir):
                os.makedirs(config_dir)
                
            config_dict = self._config_to_dict(config)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
                         
            logger.info(f"配置文件保存成功: {self.config_path}")
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {e}")
            raise
            
    def get_config(self) -> GatewayConfig:
        """
        获取当前配置
        
        Returns:
            GatewayConfig: 当前配置对象
        """
        if self._config is None:
            return self.load_config()
        return self._config
        
    def _dict_to_config(self, config_dict: Dict[str, Any]) -> GatewayConfig:
        """将字典转换为配置对象"""
        ssh_dict = config_dict.get('ssh', {})
        websocket_dict = config_dict.get('websocket', {})
        log_dict = config_dict.get('log', {})
        session_dict = config_dict.get('session', {})
        security_dict = config_dict.get('security', {})
        
        return GatewayConfig(
            ssh=SSHConfig(**ssh_dict),
            websocket=WebSocketConfig(**websocket_dict),
            log=LogConfig(**log_dict),
            session=SessionConfig(**session_dict),
            security=SecurityConfig(**security_dict)
        )
        
    def _config_to_dict(self, config: GatewayConfig) -> Dict[str, Any]:
        """将配置对象转换为字典"""
        return {
            'ssh': {
                'host': config.ssh.host,
                'port': config.ssh.port,
                'server_key_path': config.ssh.server_key_path,
                'authorized_keys_path': config.ssh.authorized_keys_path,
                'password_auth': config.ssh.password_auth,
                'default_user': config.ssh.default_user
            },
            'websocket': {
                'host': config.websocket.host,
                'port': config.websocket.port,
                'ssl_cert_path': config.websocket.ssl_cert_path,
                'ssl_key_path': config.websocket.ssl_key_path,
                'ssl_enabled': config.websocket.ssl_enabled
            },
            'log': {
                'level': config.log.level,
                'file': config.log.file,
                'console': config.log.console,
                'max_size': config.log.max_size,
                'backup_count': config.log.backup_count
            },
            'session': {
                'device_timeout': config.session.device_timeout,
                'ssh_timeout': config.session.ssh_timeout,
                'heartbeat_interval': config.session.heartbeat_interval
            },
            'security': {
                'device_id_validation': config.security.device_id_validation,
                'device_id_pattern': config.security.device_id_pattern,
                'max_connections': config.security.max_connections
            }
        }
        
    def _validate_config(self):
        """验证配置的有效性"""
        if self._config is None:
            raise ValueError("配置对象为空")
            
        # 验证端口范围
        if not (1 <= self._config.ssh.port <= 65535):
            raise ValueError(f"SSH端口无效: {self._config.ssh.port}")
            
        if not (1 <= self._config.websocket.port <= 65535):
            raise ValueError(f"WebSocket端口无效: {self._config.websocket.port}")
            
        # 验证日志级别
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if self._config.log.level.upper() not in valid_levels:
            raise ValueError(f"日志级别无效: {self._config.log.level}")
            
        # 验证超时时间
        if self._config.session.device_timeout <= 0:
            raise ValueError(f"设备超时时间无效: {self._config.session.device_timeout}")
            
        if self._config.session.ssh_timeout <= 0:
            raise ValueError(f"SSH超时时间无效: {self._config.session.ssh_timeout}")
            
        # 验证最大连接数
        if self._config.security.max_connections <= 0:
            raise ValueError(f"最大连接数无效: {self._config.security.max_connections}")
            
        logger.debug("配置验证通过")
        
    def update_config(self, **kwargs):
        """
        更新配置
        
        Args:
            **kwargs: 要更新的配置项
        """
        if self._config is None:
            self.load_config()
            
        # 支持嵌套更新
        for key, value in kwargs.items():
            if hasattr(self._config, key):
                if isinstance(value, dict):
                    # 更新嵌套配置
                    config_obj = getattr(self._config, key)
                    for sub_key, sub_value in value.items():
                        if hasattr(config_obj, sub_key):
                            setattr(config_obj, sub_key, sub_value)
                else:
                    setattr(self._config, key, value)
                    
        # 重新验证配置
        self._validate_config()
        
        logger.info("配置更新成功")


# 全局配置管理器实例
config_manager = ConfigManager()
